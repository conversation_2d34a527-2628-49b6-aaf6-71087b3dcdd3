# Service Order Manager

Sistema completo para gerenciamento de ordens de serviço, desenvolvido com Spring Boot (backend) e React + TypeScript (frontend).

## 🚀 Tecnologias

### Backend
- **Java 17**
- **Spring Boot 3.x**
- **Spring Data JPA**
- **PostgreSQL**
- **Maven**

### Frontend
- **React 19**
- **TypeScript**
- **Vite**
- **Tailwind CSS**
- **shadcn/ui**
- **React Router**
- **Lucide React**

## 📁 Estrutura do Projeto

```
service-order-manager/
├── backend/
│   └── os-manager/          # Spring Boot application
├── frontend/                # React + TypeScript application
├── .gitignore
└── README.md
```

## 🏗️ Funcionalidades

### Entidades Principais
- **Ordens de Serviço** - Gerenciamento completo de OS
- **Clientes** - Cadastro e gestão de clientes
- **Técnicos** - Controle de técnicos e especializações
- **Equipamentos** - Catálogo de equipamentos

### Status das Ordens
- `PENDENTE` - Aguardando atendimento
- `EM_ANDAMENTO` - Sendo executada
- `CONCLUIDA` - Finalizada com sucesso
- `CANCELADA` - Cancelada

## 🛠️ Como Executar

### Backend (Spring Boot)
```bash
cd backend/os-manager
mvn spring-boot:run
```
Acesse: `http://localhost:8080`

### Frontend (React)
```bash
cd frontend
npm install
npm run dev
```
Acesse: `http://localhost:5173`

## 📊 API Endpoints

### Ordens de Serviço
- `GET /api/service-orders` - Listar todas as ordens
- `POST /api/service-orders` - Criar nova ordem
- `GET /api/service-orders/{id}` - Buscar ordem por ID
- `PATCH /api/service-orders/{id}/status` - Atualizar status

### Clientes
- `GET /api/clients` - Listar clientes
- `POST /api/clients` - Criar cliente
- `GET /api/clients/{id}` - Buscar cliente por ID
- `PUT /api/clients/{id}` - Atualizar cliente

### Técnicos
- `GET /api/technicians` - Listar técnicos
- `POST /api/technicians` - Criar técnico
- `GET /api/technicians/{id}` - Buscar técnico por ID
- `PUT /api/technicians/{id}` - Atualizar técnico

### Equipamentos
- `GET /api/equipments` - Listar equipamentos
- `POST /api/equipments` - Criar equipamento
- `GET /api/equipments/{id}` - Buscar equipamento por ID
- `PUT /api/equipments/{id}` - Atualizar equipamento

## 🎨 Interface

### Dashboard
- Estatísticas gerais do sistema
- Ordens recentes
- Indicadores visuais

### Páginas Principais
- **Ordens de Serviço** - Listagem com filtros e busca
- **Clientes** - Gestão completa de clientes
- **Técnicos** - Controle de técnicos e carga de trabalho
- **Equipamentos** - Catálogo com status e histórico

### Design
- Interface responsiva (mobile-first)
- Componentes shadcn/ui
- Tema consistente
- Navegação intuitiva

## 🔧 Configuração

### Banco de Dados
Configure o PostgreSQL no arquivo `backend/os-manager/src/main/resources/application.properties`:

```properties
spring.datasource.url=******************************************
spring.datasource.username=seu_usuario
spring.datasource.password=sua_senha
```

### Variáveis de Ambiente (Frontend)
Configure no arquivo `frontend/.env`:

```env
VITE_API_URL=http://localhost:8080/api
VITE_APP_NAME=Service Order Manager
VITE_APP_VERSION=1.0.0
```

## 📝 Desenvolvimento

### Commits Organizados
O projeto foi desenvolvido com commits estruturados:
1. Configuração inicial do backend
2. Implementação das entidades
3. Criação dos controllers e services
4. Setup do frontend
5. Implementação do layout e páginas
6. Integração com shadcn/ui

### Próximos Passos
- [ ] Integração completa frontend-backend
- [ ] Formulários de criação/edição
- [ ] Autenticação e autorização
- [ ] Relatórios e dashboards avançados
- [ ] Notificações em tempo real
- [ ] Testes automatizados

## 👥 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Contato

Pedro Abib - [GitHub](https://github.com/pedroabib)

Link do Projeto: [https://github.com/pedroabib/service-order-manager](https://github.com/pedroabib/service-order-manager)
