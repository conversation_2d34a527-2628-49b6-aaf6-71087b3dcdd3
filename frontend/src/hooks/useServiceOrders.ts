import { useApi, useApiMutation } from './useApi';
import { serviceOrderService } from '@/services';
import { ServiceOrderCreateDTO, ServiceOrderStatusUpdateDTO } from '@/types';
import { useSimpleApi } from './useSimpleApi';

// Hook para listar todas as ordens - versão simplificada para debug
export function useServiceOrders() {
  return useSimpleApi<any[]>('http://localhost:8080/api/service-orders');
}

// Hook para uma ordem específica
export function useServiceOrder(id: number) {
  return useApi(() => serviceOrderService.getById(id), { immediate: !!id });
}

// Hook para estatísticas das ordens - versão simplificada
export function useServiceOrderStats() {
  const { data: orders, loading, error } = useSimpleApi<any[]>('http://localhost:8080/api/service-orders');

  if (loading || error || !orders) {
    return { data: null, loading, error };
  }

  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'PENDENTE').length,
    inProgress: orders.filter(o => o.status === 'EM_ANDAMENTO').length,
    completed: orders.filter(o => o.status === 'CONCLUIDA').length,
    cancelled: orders.filter(o => o.status === 'CANCELADA').length,
  };

  return { data: stats, loading: false, error: null };
}

// Hook para ordens recentes - versão simplificada
export function useRecentServiceOrders(limit: number = 5) {
  const { data: orders, loading, error } = useSimpleApi<any[]>('http://localhost:8080/api/service-orders');

  if (loading || error || !orders) {
    return { data: [], loading, error };
  }

  const recentOrders = orders
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, limit);

  return { data: recentOrders, loading: false, error: null };
}

// Hook para criar ordem
export function useCreateServiceOrder() {
  return useApiMutation((data: ServiceOrderCreateDTO) => 
    serviceOrderService.create(data)
  );
}

// Hook para atualizar ordem de serviço
export function useUpdateServiceOrder() {
  return useApiMutation(({ id, data }: { id: number; data: Partial<ServiceOrderCreateDTO> }) =>
    serviceOrderService.update(id, data)
  );
}

// Hook para atualizar status da ordem
export function useUpdateServiceOrderStatus() {
  return useApiMutation(({ id, data }: { id: number; data: ServiceOrderStatusUpdateDTO }) =>
    serviceOrderService.updateStatus(id, data)
  );
}

// Hook para deletar ordem
export function useDeleteServiceOrder() {
  return useApiMutation((id: number) => serviceOrderService.delete(id));
}
