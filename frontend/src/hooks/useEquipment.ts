import { useApi, useApiMutation } from './useApi';
import { equipmentService } from '@/services';
import { EquipmentCreateDTO } from '@/types';
import { useSimpleApi } from './useSimpleApi';

// Hook para listar todos os equipamentos - versão simplificada
export function useEquipment() {
  return useSimpleApi<any[]>('http://localhost:8080/api/equipments');
}

// Hook para um equipamento específico
export function useEquipmentById(id: number) {
  return useApi(() => equipmentService.getById(id), { immediate: !!id });
}

// Hook para estatísticas dos equipamentos - versão simplificada
export function useEquipmentStats() {
  const { data: equipment, loading, error } = useSimpleApi<any[]>('http://localhost:8080/api/equipments');

  if (loading || error || !equipment) {
    return { data: null, loading, error };
  }

  const stats = {
    total: equipment.length,
    active: equipment.filter(e => (e.totalServiceOrders || 0) > 0).length,
    maintenance: 0, // Simulado
    inactive: equipment.filter(e => (e.totalServiceOrders || 0) === 0).length,
  };

  return { data: stats, loading: false, error: null };
}

// Hook para buscar equipamentos
export function useSearchEquipment() {
  return useApiMutation((term: string) => equipmentService.search(term));
}

// Hook para criar equipamento
export function useCreateEquipment() {
  return useApiMutation((data: EquipmentCreateDTO) => 
    equipmentService.create(data)
  );
}

// Hook para atualizar equipamento
export function useUpdateEquipment() {
  return useApiMutation(({ id, data }: { id: number; data: Partial<EquipmentCreateDTO> }) =>
    equipmentService.update(id, data)
  );
}

// Hook para deletar equipamento
export function useDeleteEquipment() {
  return useApiMutation((id: number) => equipmentService.delete(id));
}
