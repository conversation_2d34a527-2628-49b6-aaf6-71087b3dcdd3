import { useApi, useApiMutation } from './useApi';
import { technicianService } from '@/services';
import { TechnicianCreateDTO } from '@/types';
import { useSimpleApi } from './useSimpleApi';

// Hook para listar todos os técnicos - versão simplificada
export function useTechnicians() {
  return useSimpleApi<any[]>('http://localhost:8080/api/technicians');
}

// Hook para um técnico específico
export function useTechnician(id: number) {
  return useApi(() => technicianService.getById(id), { immediate: !!id });
}

// Hook para estatísticas dos técnicos - versão simplificada
export function useTechnicianStats() {
  const { data: technicians, loading, error } = useSimpleApi<any[]>('http://localhost:8080/api/technicians');

  if (loading || error || !technicians) {
    return { data: null, loading, error };
  }

  const stats = {
    total: technicians.length,
    available: technicians.filter(t => (t.totalServiceOrders || 0) === 0).length,
    active: technicians.filter(t => (t.totalServiceOrders || 0) > 0).length,
    totalActiveOrders: technicians.reduce((sum, t) => sum + (t.totalServiceOrders || 0), 0),
  };

  return { data: stats, loading: false, error: null };
}

// Hook para buscar técnicos
export function useSearchTechnicians() {
  return useApiMutation((term: string) => technicianService.search(term));
}

// Hook para criar técnico
export function useCreateTechnician() {
  return useApiMutation((data: TechnicianCreateDTO) => 
    technicianService.create(data)
  );
}

// Hook para atualizar técnico
export function useUpdateTechnician() {
  return useApiMutation(({ id, data }: { id: number; data: Partial<TechnicianCreateDTO> }) =>
    technicianService.update(id, data)
  );
}

// Hook para deletar técnico
export function useDeleteTechnician() {
  return useApiMutation((id: number) => technicianService.delete(id));
}
