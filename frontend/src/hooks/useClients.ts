import { useApi, useApiMutation } from './useApi';
import { clientService } from '@/services';
import { ClientCreateDTO } from '@/types';
import { useSimpleApi } from './useSimpleApi';

// Hook para listar todos os clientes - versão simplificada
export function useClients() {
  return useSimpleApi<any[]>('http://localhost:8080/api/clients');
}

// Hook para um cliente específico
export function useClient(id: number) {
  return useApi(() => clientService.getById(id), { immediate: !!id });
}

// Hook para estatísticas dos clientes - versão simplificada
export function useClientStats() {
  const { data: clients, loading, error } = useSimpleApi<any[]>('http://localhost:8080/api/clients');

  if (loading || error || !clients) {
    return { data: null, loading, error };
  }

  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const stats = {
    total: clients.length,
    active: clients.filter(c => (c.totalServiceOrders || 0) > 0).length,
    newThisMonth: clients.filter(c =>
      new Date(c.createdAt) >= startOfMonth
    ).length,
  };

  return { data: stats, loading: false, error: null };
}

// Hook para buscar clientes
export function useSearchClients() {
  return useApiMutation((term: string) => clientService.search(term));
}

// Hook para criar cliente
export function useCreateClient() {
  return useApiMutation((data: ClientCreateDTO) => 
    clientService.create(data)
  );
}

// Hook para atualizar cliente
export function useUpdateClient() {
  return useApiMutation(({ id, data }: { id: number; data: Partial<ClientCreateDTO> }) =>
    clientService.update(id, data)
  );
}

// Hook para deletar cliente
export function useDeleteClient() {
  return useApiMutation((id: number) => clientService.delete(id));
}
