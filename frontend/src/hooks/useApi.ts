import { useState, useEffect, useCallback, useRef } from 'react';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiOptions {
  immediate?: boolean;
}

export function useApi<T>(
  apiCall: () => Promise<T>,
  options: UseApiOptions = { immediate: true }
) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  // Usar ref para manter a referência da função sem causar re-renders
  const apiCallRef = useRef(apiCall);
  apiCallRef.current = apiCall;

  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await apiCallRef.current();
      setState({ data: result, loading: false, error: null });
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      setState(prev => ({ ...prev, loading: false, error: errorMessage }));
      throw error;
    }
  }, []);

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null });
  }, []);

  useEffect(() => {
    if (options.immediate) {
      execute();
    }
  }, [execute, options.immediate]);

  return {
    ...state,
    execute,
    reset,
    refetch: execute,
  };
}

// Hook específico para listas
export function useApiList<T>(
  apiCall: () => Promise<T[]>,
  options: UseApiOptions = { immediate: true }
) {
  const result = useApi(apiCall, options);
  
  return {
    ...result,
    data: result.data || [],
  };
}

// Hook para operações que não retornam dados (create, update, delete)
export function useApiMutation<TData, TVariables = void>(
  apiCall: (variables: TVariables) => Promise<TData>
) {
  const [state, setState] = useState<{
    loading: boolean;
    error: string | null;
  }>({
    loading: false,
    error: null,
  });

  // Usar ref para manter a referência da função
  const apiCallRef = useRef(apiCall);
  apiCallRef.current = apiCall;

  const mutate = useCallback(async (variables: TVariables) => {
    setState({ loading: true, error: null });

    try {
      const result = await apiCallRef.current(variables);
      setState({ loading: false, error: null });
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      setState({ loading: false, error: errorMessage });
      throw error;
    }
  }, []);

  const reset = useCallback(() => {
    setState({ loading: false, error: null });
  }, []);

  return {
    ...state,
    mutate,
    reset,
  };
}
