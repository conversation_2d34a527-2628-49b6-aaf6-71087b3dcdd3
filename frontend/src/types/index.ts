// Service Order types (matching backend)
export interface ServiceOrder {
  id: number;
  orderNumber: string;
  createdAt: string;
  status: ServiceOrderStatus;
  description?: string;
  observations?: string;
  // Client info
  clientId: number;
  clientName: string;
  clientEmail?: string;
  // Technician info
  technicianId?: number;
  technicianName?: string;
  technicianEmail?: string;
  // Equipment info
  equipments: EquipmentSummary[];
}

export const ServiceOrderStatus = {
  PENDENTE: 'PENDENTE',
  EM_ANDAMENTO: 'EM_ANDAMENTO',
  CONCLUIDA: 'CONCLUIDA',
  CANCELADA: 'CANCELADA'
} as const;

export type ServiceOrderStatus = typeof ServiceOrderStatus[keyof typeof ServiceOrderStatus];

export interface EquipmentSummary {
  id: number;
  name: string;
  model?: string;
  brand?: string;
  serialNumber?: string;
}

// Client types (matching backend)
export interface Client {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  createdAt: string;
  totalServiceOrders?: number;
}

// Technician types (matching backend)
export interface Technician {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  specialization?: string;
  createdAt: string;
  totalServiceOrders?: number;
}

// Equipment types (matching backend)
export interface Equipment {
  id: number;
  name: string;
  model?: string;
  brand?: string;
  serialNumber?: string;
  description?: string;
  createdAt: string;
  totalServiceOrders?: number;
  // Campo específico para descrição do problema em uma OS
  issueDescription?: string;
}

// DTOs for creating/updating
export interface ServiceOrderCreateDTO {
  clientId: number;
  technicianId?: number;
  equipmentIds?: number[];
  description?: string;
  observations?: string;
}

export interface ServiceOrderStatusUpdateDTO {
  status: ServiceOrderStatus;
  observations?: string;
}

export interface ClientCreateDTO {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface TechnicianCreateDTO {
  name: string;
  email?: string;
  phone?: string;
  specialization?: string;
}

export interface EquipmentCreateDTO {
  name: string;
  model?: string;
  brand?: string;
  serialNumber?: string;
  description?: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
