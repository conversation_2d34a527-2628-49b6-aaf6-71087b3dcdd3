import { z } from "zod"

export const equipmentFormSchema = z.object({
  name: z
    .string()
    .min(2, "Nome deve ter pelo menos 2 caracteres")
    .max(100, "Nome deve ter no máximo 100 caracteres"),
  model: z
    .string()
    .max(50, "Modelo deve ter no máximo 50 caracteres")
    .optional()
    .or(z.literal("")),
  brand: z
    .string()
    .max(50, "Marca deve ter no máximo 50 caracteres")
    .optional()
    .or(z.literal("")),
  serialNumber: z
    .string()
    .max(100, "Número de série deve ter no máximo 100 caracteres")
    .optional()
    .or(z.literal("")),
  description: z
    .string()
    .max(500, "Descrição deve ter no máximo 500 caracteres")
    .optional()
    .or(z.literal("")),
})

export type EquipmentFormData = z.infer<typeof equipmentFormSchema>
