import { z } from "zod"

export const serviceOrderFormSchema = z.object({
  clientId: z
    .number()
    .min(1, "Cliente é obrigatório"),
  technicianId: z
    .number()
    .optional(),
  equipmentIds: z
    .array(z.number())
    .default([]),
  description: z
    .string()
    .max(1000, "Descrição deve ter no máximo 1000 caracteres")
    .optional()
    .or(z.literal("")),
  observations: z
    .string()
    .max(1000, "Observações devem ter no máximo 1000 caracteres")
    .optional()
    .or(z.literal("")),
})

export type ServiceOrderFormData = z.infer<typeof serviceOrderFormSchema>
