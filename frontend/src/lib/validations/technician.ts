import { z } from "zod"

export const technicianFormSchema = z.object({
  name: z
    .string()
    .min(2, "Nome deve ter pelo menos 2 caracteres")
    .max(100, "Nome deve ter no máximo 100 caracteres"),
  email: z
    .string()
    .email("Email inválido")
    .optional()
    .or(z.literal("")),
  phone: z
    .string()
    .optional()
    .or(z.literal("")),
  specialization: z
    .string()
    .max(100, "Especialização deve ter no máximo 100 caracteres")
    .optional()
    .or(z.literal("")),
})

export type TechnicianFormData = z.infer<typeof technicianFormSchema>
