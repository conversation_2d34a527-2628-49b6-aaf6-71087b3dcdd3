import './App.css'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Toaster } from 'sonner'
import AppLayout from '@/components/layout/AppLayout'
import Dashboard from '@/pages/Dashboard'
import ServiceOrders from '@/pages/ServiceOrders'
import ServiceOrderDetail from '@/pages/ServiceOrderDetail'
import Clients from '@/pages/Clients'
import Technicians from '@/pages/Technicians'
import Equipment from '@/pages/Equipment'
import TestApi from '@/pages/TestApi'

function App() {
  return (
    <>
      <Router>
        <Routes>
          <Route path="/test" element={<TestApi />} />
          <Route path="/" element={<AppLayout />}>
            <Route index element={<Dashboard />} />
            <Route path="service-orders" element={<ServiceOrders />} />
            <Route path="service-orders/:id" element={<ServiceOrderDetail />} />
            <Route path="clients" element={<Clients />} />
            <Route path="technicians" element={<Technicians />} />
            <Route path="equipment" element={<Equipment />} />
          </Route>
        </Routes>
      </Router>
      <Toaster position="top-right" />
    </>
  )
}

export default App
