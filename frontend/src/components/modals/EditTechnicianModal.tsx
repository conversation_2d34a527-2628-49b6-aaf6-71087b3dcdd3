import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { TechnicianForm } from "@/components/forms/TechnicianForm"
import { useUpdateTechnician } from "@/hooks"
import { TechnicianFormData } from "@/lib/validations/technician"
import { Technician } from "@/types"
import { toast } from "sonner"

interface EditTechnicianModalProps {
  technician: Technician | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function EditTechnicianModal({ technician, open, onOpenChange, onSuccess }: EditTechnicianModalProps) {
  const { mutate: updateTechnician, loading, error } = useUpdateTechnician()

  const handleSubmit = async (data: TechnicianFormData) => {
    if (!technician) return

    try {
      await updateTechnician({ id: technician.id, data })
      onOpenChange(false)
      onSuccess?.()
      toast.success("Técnico atualizado com sucesso!")
    } catch (error) {
      toast.error("Erro ao atualizar técnico. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  if (!technician) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Editar Técnico</DialogTitle>
          <DialogDescription>
            Atualize as informações do técnico. Campos marcados com * são obrigatórios.
          </DialogDescription>
        </DialogHeader>
        
        <TechnicianForm
          technician={technician}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={loading}
        />
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
