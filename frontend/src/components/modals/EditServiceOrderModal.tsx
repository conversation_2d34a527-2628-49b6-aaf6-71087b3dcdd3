import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ServiceOrderForm } from "@/components/forms/ServiceOrderForm"
import { useUpdateServiceOrder } from "@/hooks"
import { ServiceOrder } from "@/types"
import { toast } from "sonner"

interface ServiceOrderFormData {
  clientId: number
  technicianId?: number
  equipmentIds: number[]
  description?: string
  observations?: string
}

interface EditServiceOrderModalProps {
  serviceOrder: ServiceOrder | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function EditServiceOrderModal({ serviceOrder, open, onOpenChange, onSuccess }: EditServiceOrderModalProps) {
  const { mutate: updateServiceOrder, loading, error } = useUpdateServiceOrder()

  const handleSubmit = async (data: ServiceOrderFormData) => {
    if (!serviceOrder) return

    try {
      // Converter null para undefined para compatibilidade com o DTO
      const submitData = {
        ...data,
        technicianId: data.technicianId || undefined,
      }
      await updateServiceOrder({ id: serviceOrder.id, data: submitData })
      onOpenChange(false)
      onSuccess?.()
      toast.success("Ordem de serviço atualizada com sucesso!")
    } catch (error) {
      toast.error("Erro ao atualizar ordem de serviço. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  if (!serviceOrder) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Editar Ordem de Serviço</DialogTitle>
          <DialogDescription>
            Atualize as informações da ordem de serviço #{serviceOrder.orderNumber}. 
            Campos marcados com * são obrigatórios.
          </DialogDescription>
        </DialogHeader>
        
        <ServiceOrderForm
          serviceOrder={serviceOrder}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={loading}
        />
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
