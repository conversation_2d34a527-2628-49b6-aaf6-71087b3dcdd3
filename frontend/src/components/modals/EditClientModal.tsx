
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ClientForm } from "@/components/forms/ClientForm"
import { useUpdateClient } from "@/hooks"
import { ClientFormData } from "@/lib/validations/client"
import { Client } from "@/types"
import { toast } from "sonner"

interface EditClientModalProps {
  client: Client | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function EditClientModal({ client, open, onOpenChange, onSuccess }: EditClientModalProps) {
  const { mutate: updateClient, loading, error } = useUpdateClient()

  const handleSubmit = async (data: ClientFormData) => {
    if (!client) return

    try {
      await updateClient({ id: client.id, data })
      onOpenChange(false)
      onSuccess?.()
      toast.success("Cliente atualizado com sucesso!")
    } catch (error) {
      toast.error("Erro ao atualizar cliente. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  if (!client) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Editar Cliente</DialogTitle>
          <DialogDescription>
            Atualize as informações do cliente. Campos marcados com * são obrigatórios.
          </DialogDescription>
        </DialogHeader>
        
        <ClientForm
          client={client}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={loading}
        />
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
