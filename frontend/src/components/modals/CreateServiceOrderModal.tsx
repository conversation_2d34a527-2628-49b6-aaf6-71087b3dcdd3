import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ServiceOrderForm } from "@/components/forms/ServiceOrderForm"
import { useCreateServiceOrder } from "@/hooks"
import { toast } from "sonner"

interface ServiceOrderFormData {
  clientId: number
  technicianId?: number
  equipmentIds: number[]
  description?: string
  observations?: string
}

interface CreateServiceOrderModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function CreateServiceOrderModal({ open, onOpenChange, onSuccess }: CreateServiceOrderModalProps) {
  const { mutate: createServiceOrder, loading, error } = useCreateServiceOrder()

  const handleSubmit = async (data: ServiceOrderFormData) => {
    try {
      // Converter null para undefined para compatibilidade com o DTO
      const submitData = {
        ...data,
        technicianId: data.technicianId || undefined,
      }
      await createServiceOrder(submitData)
      onOpenChange(false)
      onSuccess?.()
      toast.success("Ordem de serviço criada com sucesso!")
    } catch (error) {
      toast.error("Erro ao criar ordem de serviço. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Nova Ordem de Serviço</DialogTitle>
          <DialogDescription>
            Preencha as informações da nova ordem de serviço. Campos marcados com * são obrigatórios.
          </DialogDescription>
        </DialogHeader>
        
        <ServiceOrderForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={loading}
        />
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
