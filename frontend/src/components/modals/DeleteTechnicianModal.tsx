import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { useDeleteTechnician } from "@/hooks"
import { Technician } from "@/types"
import { toast } from "sonner"
import { Loader2, AlertTriangle } from "lucide-react"

interface DeleteTechnicianModalProps {
  technician: Technician | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function DeleteTechnicianModal({ technician, open, onOpenChange, onSuccess }: DeleteTechnicianModalProps) {
  const { mutate: deleteTechnician, loading, error } = useDeleteTechnician()

  const handleDelete = async () => {
    if (!technician) return

    try {
      await deleteTechnician(technician.id)
      onOpenChange(false)
      onSuccess?.()
      toast.success("Técnico excluído com sucesso!")
    } catch (error) {
      toast.error("Erro ao excluir técnico. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  if (!technician) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Confirmar Exclusão
          </DialogTitle>
          <DialogDescription>
            Tem certeza que deseja excluir o técnico <strong>{technician.name}</strong>?
            Esta ação não pode ser desfeita.
          </DialogDescription>
        </DialogHeader>
        
        {(technician.totalServiceOrders ?? 0) > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <p className="text-sm text-yellow-800">
              <strong>Atenção:</strong> Este técnico possui {technician.totalServiceOrders} ordem{technician.totalServiceOrders === 1 ? '' : 's'} de serviço associada{technician.totalServiceOrders === 1 ? '' : 's'}.
            </p>
          </div>
        )}

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={loading}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Excluir Técnico
          </Button>
        </DialogFooter>
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
