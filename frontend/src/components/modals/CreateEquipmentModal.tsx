import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { EquipmentForm } from "@/components/forms/EquipmentForm"
import { useCreateEquipment } from "@/hooks"
import { EquipmentFormData } from "@/lib/validations/equipment"
import { toast } from "sonner"

interface CreateEquipmentModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function CreateEquipmentModal({ open, onOpenChange, onSuccess }: CreateEquipmentModalProps) {
  const { mutate: createEquipment, loading, error } = useCreateEquipment()

  const handleSubmit = async (data: EquipmentFormData) => {
    try {
      await createEquipment(data)
      onOpenChange(false)
      onSuccess?.()
      toast.success("Equipamento criado com sucesso!")
    } catch (error) {
      toast.error("Erro ao criar equipamento. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Novo Equipamento</DialogTitle>
          <DialogDescription>
            Preencha as informações do novo equipamento. Campos marcados com * são obrigatórios.
          </DialogDescription>
        </DialogHeader>
        
        <EquipmentForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={loading}
        />
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
