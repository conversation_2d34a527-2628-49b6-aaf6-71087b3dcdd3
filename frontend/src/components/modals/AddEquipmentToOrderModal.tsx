import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Loader2 } from 'lucide-react'
import { useEquipment } from '@/hooks'
import { EquipmentSummary } from '@/types'

interface EquipmentWithDescription {
  equipment: EquipmentSummary
  description: string
}

interface AddEquipmentToOrderModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onAdd: (equipmentWithDescription: EquipmentWithDescription) => void
  excludeEquipmentIds?: number[]
}

export function AddEquipmentToOrderModal({
  open,
  onOpenChange,
  onAdd,
  excludeEquipmentIds = []
}: AddEquipmentToOrderModalProps) {
  const [selectedEquipmentId, setSelectedEquipmentId] = useState<string>('')
  const [description, setDescription] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Hook para carregar equipamentos
  const { data: equipment, loading: equipmentLoading } = useEquipment()

  // Filtrar equipamentos já adicionados
  const availableEquipment = equipment?.filter(
    eq => !excludeEquipmentIds.includes(eq.id)
  ) || []

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedEquipmentId || !description.trim()) {
      return
    }

    const selectedEquipment = availableEquipment.find(
      eq => eq.id.toString() === selectedEquipmentId
    )

    if (!selectedEquipment) {
      return
    }

    setIsSubmitting(true)

    try {
      await onAdd({
        equipment: selectedEquipment,
        description: description.trim()
      })

      // Reset form
      setSelectedEquipmentId('')
      setDescription('')
      onOpenChange(false)
    } catch (error) {
      console.error('Erro ao adicionar equipamento:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    setSelectedEquipmentId('')
    setDescription('')
    onOpenChange(false)
  }

  const isFormValid = selectedEquipmentId && description.trim()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Adicionar Equipamento</DialogTitle>
          <DialogDescription>
            Selecione um equipamento e descreva o problema específico
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="equipment">Equipamento *</Label>
            <Select
              value={selectedEquipmentId}
              onValueChange={setSelectedEquipmentId}
              disabled={equipmentLoading || isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione um equipamento" />
              </SelectTrigger>
              <SelectContent>
                {equipmentLoading ? (
                  <SelectItem value="loading" disabled>
                    <div className="flex items-center">
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      Carregando equipamentos...
                    </div>
                  </SelectItem>
                ) : availableEquipment.length === 0 ? (
                  <SelectItem value="no-equipment" disabled>
                    Nenhum equipamento disponível
                  </SelectItem>
                ) : (
                  availableEquipment.map((eq) => (
                    <SelectItem key={eq.id} value={eq.id.toString()}>
                      <div>
                        <div className="font-medium">{eq.name}</div>
                        <div className="text-sm text-gray-600">
                          {eq.brand} {eq.model}
                          {eq.serialNumber && ` - SN: ${eq.serialNumber}`}
                        </div>
                      </div>
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descrição do Problema *</Label>
            <Textarea
              id="description"
              placeholder="Descreva o problema específico deste equipamento..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              disabled={isSubmitting}
              rows={4}
              className="resize-none"
            />
            <p className="text-sm text-gray-500">
              Seja específico sobre o problema ou serviço necessário para este equipamento
            </p>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={!isFormValid || isSubmitting}
            >
              {isSubmitting && <Loader2 className="w-4 h-4 animate-spin mr-2" />}
              Adicionar Equipamento
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
