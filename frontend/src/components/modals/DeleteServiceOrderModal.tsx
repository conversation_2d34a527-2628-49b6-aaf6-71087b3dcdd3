import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useDeleteServiceOrder } from "@/hooks"
import { ServiceOrder } from "@/types"
import { toast } from "sonner"
import { Loader2, AlertTriangle } from "lucide-react"

interface DeleteServiceOrderModalProps {
  serviceOrder: ServiceOrder | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function DeleteServiceOrderModal({ serviceOrder, open, onOpenChange, onSuccess }: DeleteServiceOrderModalProps) {
  const { mutate: deleteServiceOrder, loading, error } = useDeleteServiceOrder()

  const handleDelete = async () => {
    if (!serviceOrder) return

    try {
      await deleteServiceOrder(serviceOrder.id)
      onOpenChange(false)
      onSuccess?.()
      toast.success("Ordem de serviço excluída com sucesso!")
    } catch (error) {
      toast.error("Erro ao excluir ordem de serviço. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  if (!serviceOrder) return null

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDENTE': return 'text-yellow-600'
      case 'EM_ANDAMENTO': return 'text-blue-600'
      case 'CONCLUIDA': return 'text-green-600'
      case 'CANCELADA': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDENTE': return 'Pendente'
      case 'EM_ANDAMENTO': return 'Em Andamento'
      case 'CONCLUIDA': return 'Concluída'
      case 'CANCELADA': return 'Cancelada'
      default: return status
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Confirmar Exclusão
          </DialogTitle>
          <DialogDescription>
            Tem certeza que deseja excluir a ordem de serviço <strong>#{serviceOrder.orderNumber}</strong>?
            Esta ação não pode ser desfeita.
          </DialogDescription>
        </DialogHeader>
        
        <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
          <div className="space-y-1 text-sm">
            <p><strong>Cliente:</strong> {serviceOrder.clientName}</p>
            <p><strong>Status:</strong> <span className={getStatusColor(serviceOrder.status)}>{getStatusLabel(serviceOrder.status)}</span></p>
            {serviceOrder.technicianName && (
              <p><strong>Técnico:</strong> {serviceOrder.technicianName}</p>
            )}
            {serviceOrder.equipments.length > 0 && (
              <p><strong>Equipamentos:</strong> {serviceOrder.equipments.length} item(ns)</p>
            )}
          </div>
        </div>

        {serviceOrder.status === 'EM_ANDAMENTO' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <p className="text-sm text-yellow-800">
              <strong>Atenção:</strong> Esta ordem de serviço está em andamento. 
              Considere cancelá-la em vez de excluí-la.
            </p>
          </div>
        )}

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={loading}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Excluir Ordem de Serviço
          </Button>
        </DialogFooter>
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
