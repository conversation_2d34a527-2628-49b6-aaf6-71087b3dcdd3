
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ClientForm } from "@/components/forms/ClientForm"
import { useCreateClient } from "@/hooks"
import { ClientFormData } from "@/lib/validations/client"
import { toast } from "sonner"

interface CreateClientModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function CreateClientModal({ open, onOpenChange, onSuccess }: CreateClientModalProps) {
  const { mutate: createClient, loading, error } = useCreateClient()

  const handleSubmit = async (data: ClientFormData) => {
    try {
      await createClient(data)
      onOpenChange(false)
      onSuccess?.()
      toast.success("Cliente criado com sucesso!")
    } catch (error) {
      toast.error("Erro ao criar cliente. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Novo Cliente</DialogTitle>
          <DialogDescription>
            Preencha as informações do novo cliente. Campos marcados com * são obrigatórios.
          </DialogDescription>
        </DialogHeader>
        
        <ClientForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={loading}
        />
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
