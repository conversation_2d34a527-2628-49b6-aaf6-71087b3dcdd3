import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useDeleteEquipment } from "@/hooks"
import { Equipment } from "@/types"
import { toast } from "sonner"
import { Loader2, AlertTriangle } from "lucide-react"

interface DeleteEquipmentModalProps {
  equipment: Equipment | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function DeleteEquipmentModal({ equipment, open, onOpenChange, onSuccess }: DeleteEquipmentModalProps) {
  const { mutate: deleteEquipment, loading, error } = useDeleteEquipment()

  const handleDelete = async () => {
    if (!equipment) return

    try {
      await deleteEquipment(equipment.id)
      onOpenChange(false)
      onSuccess?.()
      toast.success("Equipamento excluído com sucesso!")
    } catch (error) {
      toast.error("Erro ao excluir equipamento. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  if (!equipment) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Confirmar Exclusão
          </DialogTitle>
          <DialogDescription>
            Tem certeza que deseja excluir o equipamento <strong>{equipment.name}</strong>?
            Esta ação não pode ser desfeita.
          </DialogDescription>
        </DialogHeader>
        
        {(equipment.totalServiceOrders ?? 0) > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <p className="text-sm text-yellow-800">
              <strong>Atenção:</strong> Este equipamento possui {equipment.totalServiceOrders} ordem{equipment.totalServiceOrders === 1 ? '' : 's'} de serviço associada{equipment.totalServiceOrders === 1 ? '' : 's'}.
            </p>
          </div>
        )}

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={loading}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Excluir Equipamento
          </Button>
        </DialogFooter>
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
