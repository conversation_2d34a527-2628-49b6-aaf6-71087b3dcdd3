import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { EquipmentForm } from "@/components/forms/EquipmentForm"
import { useUpdateEquipment } from "@/hooks"
import { EquipmentFormData } from "@/lib/validations/equipment"
import { Equipment } from "@/types"
import { toast } from "sonner"

interface EditEquipmentModalProps {
  equipment: Equipment | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function EditEquipmentModal({ equipment, open, onOpenChange, onSuccess }: EditEquipmentModalProps) {
  const { mutate: updateEquipment, loading, error } = useUpdateEquipment()

  const handleSubmit = async (data: EquipmentFormData) => {
    if (!equipment) return

    try {
      await updateEquipment({ id: equipment.id, data })
      onOpenChange(false)
      onSuccess?.()
      toast.success("Equipamento atualizado com sucesso!")
    } catch (error) {
      toast.error("Erro ao atualizar equipamento. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  if (!equipment) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Editar Equipamento</DialogTitle>
          <DialogDescription>
            Atualize as informações do equipamento. Campos marcados com * são obrigatórios.
          </DialogDescription>
        </DialogHeader>
        
        <EquipmentForm
          equipment={equipment}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={loading}
        />
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
