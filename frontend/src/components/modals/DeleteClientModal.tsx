import {
  <PERSON>alog,
  DialogContent,
  <PERSON>alogDes<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useDeleteClient } from "@/hooks"
import { Client } from "@/types"
import { toast } from "sonner"
import { Loader2, AlertTriangle } from "lucide-react"

interface DeleteClientModalProps {
  client: Client | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function DeleteClientModal({ client, open, onOpenChange, onSuccess }: DeleteClientModalProps) {
  const { mutate: deleteClient, loading, error } = useDeleteClient()

  const handleDelete = async () => {
    if (!client) return

    try {
      await deleteClient(client.id)
      onOpenChange(false)
      onSuccess?.()
      toast.success("Cliente excluído com sucesso!")
    } catch (error) {
      toast.error("Erro ao excluir cliente. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  if (!client) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Confirmar Exclusão
          </DialogTitle>
          <DialogDescription>
            Tem certeza que deseja excluir o cliente <strong>{client.name}</strong>?
            Esta ação não pode ser desfeita.
          </DialogDescription>
        </DialogHeader>
        
        {(client.totalServiceOrders ?? 0) > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <p className="text-sm text-yellow-800">
              <strong>Atenção:</strong> Este cliente possui {client.totalServiceOrders} ordem{client.totalServiceOrders === 1 ? '' : 's'} de serviço associada{client.totalServiceOrders === 1 ? '' : 's'}.
            </p>
          </div>
        )}

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={loading}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Excluir Cliente
          </Button>
        </DialogFooter>
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
