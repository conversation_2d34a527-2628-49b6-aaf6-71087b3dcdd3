import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { TechnicianForm } from "@/components/forms/TechnicianForm"
import { useCreateTechnician } from "@/hooks"
import { TechnicianFormData } from "@/lib/validations/technician"
import { toast } from "sonner"

interface CreateTechnicianModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function CreateTechnicianModal({ open, onOpenChange, onSuccess }: CreateTechnicianModalProps) {
  const { mutate: createTechnician, loading, error } = useCreateTechnician()

  const handleSubmit = async (data: TechnicianFormData) => {
    try {
      await createTechnician(data)
      onOpenChange(false)
      onSuccess?.()
      toast.success("Técnico criado com sucesso!")
    } catch (error) {
      toast.error("Erro ao criar técnico. Tente novamente.")
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Novo Técnico</DialogTitle>
          <DialogDescription>
            Preencha as informações do novo técnico. Campos marcados com * são obrigatórios.
          </DialogDescription>
        </DialogHeader>
        
        <TechnicianForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={loading}
        />
        
        {error && (
          <div className="text-sm text-red-600 mt-2">
            Erro: {error}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
