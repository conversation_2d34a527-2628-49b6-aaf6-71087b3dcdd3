import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { technicianFormSchema, type TechnicianFormData } from "@/lib/validations/technician"
import { Technician } from "@/types"
import { Loader2 } from "lucide-react"

interface TechnicianFormProps {
  technician?: Technician
  onSubmit: (data: TechnicianFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function TechnicianForm({ technician, onSubmit, onCancel, isLoading = false }: TechnicianFormProps) {
  const form = useForm<TechnicianFormData>({
    resolver: zodResolver(technicianFormSchema),
    defaultValues: {
      name: technician?.name || "",
      email: technician?.email || "",
      phone: technician?.phone || "",
      specialization: technician?.specialization || "",
    },
  })

  const handleSubmit = async (data: TechnicianFormData) => {
    try {
      await onSubmit(data)
      if (!technician) {
        form.reset()
      }
    } catch (error) {
      // Error handling será feito no componente pai
      console.error("Erro ao salvar técnico:", error)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome *</FormLabel>
              <FormControl>
                <Input placeholder="Digite o nome do técnico" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input 
                    type="email" 
                    placeholder="<EMAIL>" 
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Telefone</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="(11) 99999-9999" 
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="specialization"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Especialização</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Ex: Impressoras, Computadores, Redes" 
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />



        <div className="flex justify-end space-x-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {technician ? "Atualizar" : "Criar"} Técnico
          </Button>
        </div>
      </form>
    </Form>
  )
}
