import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ServiceOrder } from "@/types"
import { useClients, useTechnicians, useEquipment } from "@/hooks"
import { Loader2, X } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface ServiceOrderFormData {
  clientId: number
  technicianId?: number
  equipmentIds: number[]
  description?: string
  observations?: string
}

interface ServiceOrderFormProps {
  serviceOrder?: ServiceOrder
  onSubmit: (data: ServiceOrderFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function ServiceOrderForm({ serviceOrder, onSubmit, onCancel, isLoading = false }: ServiceOrderFormProps) {
  // Carregar dados para os selects
  const { data: clients, loading: clientsLoading } = useClients()
  const { data: technicians, loading: techniciansLoading } = useTechnicians()
  const { data: equipment, loading: equipmentLoading } = useEquipment()

  // Estado do formulário
  const [formData, setFormData] = useState<ServiceOrderFormData>({
    clientId: serviceOrder?.clientId || 0,
    technicianId: serviceOrder?.technicianId,
    equipmentIds: serviceOrder?.equipments?.map(eq => eq.id) || [],
    description: serviceOrder?.description || "",
    observations: serviceOrder?.observations || "",
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validação simples
    const newErrors: Record<string, string> = {}
    if (!formData.clientId || formData.clientId === 0) {
      newErrors.clientId = "Cliente é obrigatório"
    }

    setErrors(newErrors)

    if (Object.keys(newErrors).length === 0) {
      try {
        await onSubmit(formData)
        if (!serviceOrder) {
          setFormData({
            clientId: 0,
            technicianId: undefined,
            equipmentIds: [],
            description: "",
            observations: "",
          })
        }
      } catch (error) {
        console.error("Erro ao salvar ordem de serviço:", error)
      }
    }
  }

  const addEquipment = (equipmentId: string) => {
    const id = parseInt(equipmentId)
    if (!formData.equipmentIds.includes(id)) {
      setFormData(prev => ({
        ...prev,
        equipmentIds: [...prev.equipmentIds, id]
      }))
    }
  }

  const removeEquipment = (equipmentId: number) => {
    setFormData(prev => ({
      ...prev,
      equipmentIds: prev.equipmentIds.filter(id => id !== equipmentId)
    }))
  }

  const getEquipmentName = (equipmentId: number) => {
    const eq = equipment?.find(e => e.id === equipmentId)
    return eq ? `${eq.name}${eq.model ? ` - ${eq.model}` : ''}` : `Equipamento ${equipmentId}`
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="clientId">Cliente *</Label>
        <Select
          onValueChange={(value) => setFormData(prev => ({ ...prev, clientId: parseInt(value) }))}
          value={formData.clientId > 0 ? formData.clientId.toString() : ""}
          disabled={clientsLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione um cliente" />
          </SelectTrigger>
          <SelectContent>
            {clients?.map((client) => (
              <SelectItem key={client.id} value={client.id.toString()}>
                {client.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.clientId && (
          <p className="text-sm text-red-600">{errors.clientId}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="technicianId">Técnico</Label>
        <Select
          onValueChange={(value) => setFormData(prev => ({
            ...prev,
            technicianId: value === "0" ? undefined : parseInt(value)
          }))}
          value={formData.technicianId?.toString() || "0"}
          disabled={techniciansLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione um técnico (opcional)" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="0">Nenhum técnico</SelectItem>
            {technicians?.map((technician) => (
              <SelectItem key={technician.id} value={technician.id.toString()}>
                {technician.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Equipamentos</Label>
        <Select onValueChange={addEquipment} disabled={equipmentLoading}>
          <SelectTrigger>
            <SelectValue placeholder="Adicionar equipamento (opcional)" />
          </SelectTrigger>
          <SelectContent>
            {equipment?.filter(eq => !formData.equipmentIds.includes(eq.id)).map((eq) => (
              <SelectItem key={eq.id} value={eq.id.toString()}>
                {eq.name}{eq.model ? ` - ${eq.model}` : ''}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {formData.equipmentIds.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-2">
            {formData.equipmentIds.map((equipmentId) => (
              <Badge key={equipmentId} variant="secondary" className="flex items-center gap-1">
                {getEquipmentName(equipmentId)}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => removeEquipment(equipmentId)}
                />
              </Badge>
            ))}
          </div>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Descrição</Label>
        <Textarea
          id="description"
          placeholder="Descrição do problema ou serviço solicitado"
          className="resize-none"
          rows={3}
          value={formData.description || ""}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="observations">Observações</Label>
        <Textarea
          id="observations"
          placeholder="Observações adicionais sobre a ordem de serviço"
          className="resize-none"
          rows={2}
          value={formData.observations || ""}
          onChange={(e) => setFormData(prev => ({ ...prev, observations: e.target.value }))}
        />
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancelar
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {serviceOrder ? "Atualizar" : "Criar"} Ordem de Serviço
        </Button>
      </div>
    </form>
  )
}
