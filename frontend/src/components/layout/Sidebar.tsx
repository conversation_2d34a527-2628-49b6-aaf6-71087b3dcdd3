import { Link, useLocation } from 'react-router-dom'
import { 
  ClipboardList, 
  Users, 
  Wrench, 
  Package, 
  BarChart3,
  Settings
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Separator } from '@/components/ui/separator'

interface SidebarProps {
  onNavigate?: () => void
}

const navigation = [
  { name: 'Dashboard', href: '/', icon: BarChart3 },
  { name: 'Ordens de Serviço', href: '/service-orders', icon: ClipboardList },
  { name: 'Client<PERSON>', href: '/clients', icon: Users },
  { name: 'Técnicos', href: '/technicians', icon: Wrench },
  { name: 'Equipamentos', href: '/equipment', icon: Package },
]

export function Sidebar({ onNavigate }: SidebarProps) {
  const location = useLocation()

  return (
    <div className="flex h-full flex-col bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex h-16 shrink-0 items-center px-6 border-b border-gray-200">
        <ClipboardList className="h-8 w-8 text-blue-600" />
        <span className="ml-2 text-xl font-bold text-gray-900">
          OS Manager
        </span>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigation.map((item) => {
          const isActive = location.pathname === item.href
          return (
            <Link
              key={item.name}
              to={item.href}
              onClick={onNavigate}
              className={cn(
                'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                isActive
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <item.icon
                className={cn(
                  'mr-3 h-5 w-5 flex-shrink-0',
                  isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                )}
              />
              {item.name}
            </Link>
          )
        })}
      </nav>

      <Separator />

      {/* Settings */}
      <div className="p-3">
        <Link
          to="/settings"
          onClick={onNavigate}
          className={cn(
            'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
            location.pathname === '/settings'
              ? 'bg-blue-50 text-blue-700'
              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
          )}
        >
          <Settings className="mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500" />
          Configurações
        </Link>
      </div>
    </div>
  )
}
