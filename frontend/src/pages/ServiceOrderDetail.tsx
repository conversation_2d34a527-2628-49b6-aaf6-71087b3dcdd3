import { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
  ArrowLeft,
  Edit,
  Save,
  X,
  Plus,
  Trash2,
  Calendar,
  User,
  Wrench,
  Package,
  Loader2
} from 'lucide-react'
import { useServiceOrder } from '@/hooks'
import { EquipmentSummary } from '@/types'
import { AddEquipmentToOrderModal } from '@/components/modals/AddEquipmentToOrderModal'

interface EquipmentIssue {
  equipmentId: number
  equipment?: EquipmentSummary
  description: string
  isNew?: boolean
}

export default function ServiceOrderDetail() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const serviceOrderId = id ? parseInt(id) : 0

  // Hooks para dados
  const { data: serviceOrder, loading, error, refetch } = useServiceOrder(serviceOrderId)

  // Estados locais
  const [isEditing, setIsEditing] = useState(false)
  const [equipmentIssues, setEquipmentIssues] = useState<EquipmentIssue[]>([])
  const [addEquipmentModalOpen, setAddEquipmentModalOpen] = useState(false)

  // Inicializar equipmentIssues quando serviceOrder carrega
  useEffect(() => {
    if (serviceOrder && serviceOrder.equipments) {
      const issues: EquipmentIssue[] = serviceOrder.equipments.map(equipment => ({
        equipmentId: equipment.id,
        equipment: equipment,
        description: '' // Por enquanto vazio, será implementado no backend
      }))
      setEquipmentIssues(issues)
    }
  }, [serviceOrder])

  const handleBack = () => {
    navigate('/service-orders')
  }

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleCancel = () => {
    setIsEditing(false)
    // Resetar para os dados originais
    if (serviceOrder && serviceOrder.equipments) {
      const issues: EquipmentIssue[] = serviceOrder.equipments.map(equipment => ({
        equipmentId: equipment.id,
        equipment: equipment,
        description: '' // Por enquanto vazio, será implementado no backend
      }))
      setEquipmentIssues(issues)
    }
  }

  const handleSave = async () => {
    // TODO: Implementar salvamento
    console.log('Salvando equipmentIssues:', equipmentIssues)
    setIsEditing(false)
  }

  const addEquipment = () => {
    setAddEquipmentModalOpen(true)
  }

  const handleAddEquipment = async (equipmentWithDescription: { equipment: EquipmentSummary; description: string }) => {
    const newIssue: EquipmentIssue = {
      equipmentId: equipmentWithDescription.equipment.id,
      equipment: equipmentWithDescription.equipment,
      description: equipmentWithDescription.description,
      isNew: true
    }

    setEquipmentIssues(prev => [...prev, newIssue])
  }

  const removeEquipment = (equipmentId: number) => {
    setEquipmentIssues(prev => prev.filter(issue => issue.equipmentId !== equipmentId))
  }

  const updateEquipmentDescription = (equipmentId: number, description: string) => {
    setEquipmentIssues(prev => 
      prev.map(issue => 
        issue.equipmentId === equipmentId 
          ? { ...issue, description }
          : issue
      )
    )
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { label: 'Pendente', variant: 'secondary' as const },
      IN_PROGRESS: { label: 'Em Andamento', variant: 'default' as const },
      COMPLETED: { label: 'Concluída', variant: 'default' as const },
      CANCELLED: { label: 'Cancelada', variant: 'destructive' as const },
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </Button>
        </div>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Carregando ordem de serviço...</span>
        </div>
      </div>
    )
  }

  // Error state
  if (error || !serviceOrder) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-red-600">Erro ao carregar ordem de serviço</p>
              <Button variant="outline" onClick={() => refetch()} className="mt-4">
                Tentar novamente
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              OS #{serviceOrder.orderNumber}
            </h1>
            <p className="text-gray-600">
              Criada em {new Date(serviceOrder.createdAt).toLocaleDateString('pt-BR')}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={handleCancel}>
                <X className="w-4 h-4 mr-2" />
                Cancelar
              </Button>
              <Button onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Salvar
              </Button>
            </>
          ) : (
            <Button onClick={handleEdit}>
              <Edit className="w-4 h-4 mr-2" />
              Editar
            </Button>
          )}
        </div>
      </div>

      {/* Status e Informações Básicas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Status</CardTitle>
          </CardHeader>
          <CardContent>
            {getStatusBadge(serviceOrder.status)}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
              <User className="w-4 h-4 mr-1" />
              Cliente
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="font-medium">{serviceOrder.clientName}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
              <Wrench className="w-4 h-4 mr-1" />
              Técnico
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="font-medium">
              {serviceOrder.technicianName || 'Não atribuído'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
              <Calendar className="w-4 h-4 mr-1" />
              Data de Criação
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="font-medium">
              {new Date(serviceOrder.createdAt).toLocaleDateString('pt-BR')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Equipamentos e Problemas */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Package className="w-5 h-5 mr-2" />
              Equipamentos e Problemas
            </CardTitle>
            {isEditing && (
              <Button variant="outline" size="sm" onClick={addEquipment}>
                <Plus className="w-4 h-4 mr-2" />
                Adicionar Equipamento
              </Button>
            )}
          </div>
          <CardDescription>
            Descreva os problemas específicos de cada equipamento
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {equipmentIssues.length === 0 ? (
            <p className="text-gray-500 text-center py-8">
              Nenhum equipamento adicionado
            </p>
          ) : (
            equipmentIssues.map((issue) => (
              <div key={issue.equipmentId} className={`border rounded-lg p-4 space-y-3 ${issue.isNew ? 'border-blue-200 bg-blue-50' : ''}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div>
                      <h4 className="font-medium">
                        {issue.equipment?.name || `Equipamento ${issue.equipmentId}`}
                      </h4>
                      {issue.equipment && (
                        <p className="text-sm text-gray-600">
                          {issue.equipment.brand} {issue.equipment.model}
                          {issue.equipment.serialNumber && ` - SN: ${issue.equipment.serialNumber}`}
                        </p>
                      )}
                    </div>
                    {issue.isNew && (
                      <Badge variant="secondary" className="text-xs">
                        Novo
                      </Badge>
                    )}
                  </div>
                  {isEditing && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeEquipment(issue.equipmentId)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`description-${issue.equipmentId}`}>
                    Descrição do Problema
                  </Label>
                  <Textarea
                    id={`description-${issue.equipmentId}`}
                    placeholder="Descreva o problema específico deste equipamento..."
                    value={issue.description}
                    onChange={(e) => updateEquipmentDescription(issue.equipmentId, e.target.value)}
                    disabled={!isEditing}
                    rows={3}
                  />
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* Observações */}
      <Card>
        <CardHeader>
          <CardTitle>Observações</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700">
            {serviceOrder.observations || 'Nenhuma observação adicional'}
          </p>
        </CardContent>
      </Card>

      {/* Modal para adicionar equipamento */}
      <AddEquipmentToOrderModal
        open={addEquipmentModalOpen}
        onOpenChange={setAddEquipmentModalOpen}
        onAdd={handleAddEquipment}
        excludeEquipmentIds={equipmentIssues.map(issue => issue.equipmentId)}
      />
    </div>
  )
}
