import { useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Mail,
  Phone,
  Wrench,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { useTechnicians, useTechnicianStats } from '@/hooks'
import { technicianService } from '@/services'
import { CreateTechnicianModal } from '@/components/modals/CreateTechnicianModal'
import { EditTechnicianModal } from '@/components/modals/EditTechnicianModal'
import { DeleteTechnicianModal } from '@/components/modals/DeleteTechnicianModal'
import type { Technician } from '@/types'

export default function Technicians() {
  const [searchTerm, setSearchTerm] = useState('')
  const [createModalOpen, setCreateModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [selectedTechnician, setSelectedTechnician] = useState<Technician | null>(null)

  // Hooks para dados reais da API
  const { data: technicians, loading, error, refetch } = useTechnicians()
  const { data: technicianStats, loading: statsLoading } = useTechnicianStats()

  // Filtrar técnicos baseado no termo de busca
  const filteredTechnicians = useMemo(() => {
    if (!technicians) return []

    return technicians.filter(tech =>
      tech.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (tech.email && tech.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (tech.specialization && tech.specialization.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  }, [technicians, searchTerm])

  // Handlers para os modais
  const handleCreateTechnician = () => {
    setCreateModalOpen(true)
  }

  const handleEditTechnician = (technician: Technician) => {
    setSelectedTechnician(technician)
    setEditModalOpen(true)
  }

  const handleDeleteTechnician = (technician: Technician) => {
    setSelectedTechnician(technician)
    setDeleteModalOpen(true)
  }

  const handleModalSuccess = () => {
    refetch()
  }

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Técnicos</h1>
            <p className="text-gray-600">Gerencie todos os técnicos do sistema</p>
          </div>
          <Button onClick={handleCreateTechnician}>
            <Plus className="w-4 h-4 mr-2" />
            Novo Técnico
          </Button>
        </div>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Carregando técnicos...</span>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Técnicos</h1>
            <p className="text-gray-600">Gerencie todos os técnicos do sistema</p>
          </div>
          <Button onClick={handleCreateTechnician}>
            <Plus className="w-4 h-4 mr-2" />
            Novo Técnico
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center h-64">
          <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
          <span className="text-red-600 mb-4">Erro ao carregar técnicos: {error}</span>
          <Button onClick={refetch} variant="outline">
            Tentar Novamente
          </Button>
        </div>
      </div>
    )
  }

  const getWorkloadBadge = (technician: any) => {
    const workload = technicianService.getWorkload(technician)

    switch (workload) {
      case 'available':
        return <Badge variant="outline" className="text-green-700 border-green-300">Disponível</Badge>
      case 'low':
        return <Badge variant="secondary">Baixa</Badge>
      case 'medium':
        return <Badge variant="default">Média</Badge>
      case 'high':
        return <Badge variant="destructive">Alta</Badge>
      default:
        return <Badge variant="secondary">Desconhecida</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Técnicos</h1>
          <p className="text-gray-600">Gerencie todos os técnicos do sistema</p>
        </div>
        <Button onClick={handleCreateTechnician}>
          <Plus className="w-4 h-4 mr-2" />
          Novo Técnico
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Técnicos</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <>
                <div className="text-2xl font-bold">{technicianStats?.total || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Técnicos cadastrados
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Disponíveis</CardTitle>
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <>
                <div className="text-2xl font-bold">{technicianStats?.available || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Sem ordens ativas
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Em Atividade</CardTitle>
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <>
                <div className="text-2xl font-bold">{technicianStats?.active || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Com ordens ativas
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ordens Ativas</CardTitle>
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <>
                <div className="text-2xl font-bold">{technicianStats?.totalActiveOrders || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Total em andamento
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
          <CardDescription>
            Use os filtros abaixo para encontrar técnicos específicos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nome, email ou especialização..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filtros Avançados
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Technicians Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Técnicos</CardTitle>
          <CardDescription>
            {filteredTechnicians.length} técnico(s) encontrado(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Contato</TableHead>
                  <TableHead>Especialização</TableHead>
                  <TableHead>Carga de Trabalho</TableHead>
                  <TableHead>Ordens Concluídas</TableHead>
                  <TableHead>Data de Cadastro</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTechnicians.map((technician) => (
                  <TableRow key={technician.id}>
                    <TableCell className="font-medium">
                      {technician.name}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <Mail className="w-3 h-3 mr-1 text-gray-400" />
                          {technician.email}
                        </div>
                        <div className="flex items-center text-sm">
                          <Phone className="w-3 h-3 mr-1 text-gray-400" />
                          {technician.phone}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{technician.specialization}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {getWorkloadBadge(technician)}
                        <p className="text-xs text-gray-500">
                          {technician.totalServiceOrders || 0} ordem(ns) total
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="font-medium">{technician.totalServiceOrders}</span>
                    </TableCell>
                    <TableCell>
                      {new Date(technician.createdAt).toLocaleDateString('pt-BR')}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditTechnician(technician)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteTechnician(technician)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Modais */}
      <CreateTechnicianModal
        open={createModalOpen}
        onOpenChange={setCreateModalOpen}
        onSuccess={handleModalSuccess}
      />

      <EditTechnicianModal
        technician={selectedTechnician}
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        onSuccess={handleModalSuccess}
      />

      <DeleteTechnicianModal
        technician={selectedTechnician}
        open={deleteModalOpen}
        onOpenChange={setDeleteModalOpen}
        onSuccess={handleModalSuccess}
      />
    </div>
  )
}
