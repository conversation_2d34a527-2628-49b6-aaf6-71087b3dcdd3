import { useSimpleApi } from '@/hooks/useSimpleApi';

export default function TestApi() {
  const { data, loading, error } = useSimpleApi<any[]>('http://localhost:8080/api/service-orders');

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div>
      <h1>Test API</h1>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
}
