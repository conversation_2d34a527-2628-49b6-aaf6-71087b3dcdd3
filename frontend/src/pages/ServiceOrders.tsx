import { useState, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Plus,
  Search,
  Filter,
  Clock,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Edit,
  Trash2,
  Loader2,
  Eye
} from 'lucide-react'
import { useServiceOrders } from '@/hooks'
import { CreateServiceOrderModal } from '@/components/modals/CreateServiceOrderModal'
import { EditServiceOrderModal } from '@/components/modals/EditServiceOrderModal'
import { DeleteServiceOrderModal } from '@/components/modals/DeleteServiceOrderModal'
import type { ServiceOrder } from '@/types'

export default function ServiceOrders() {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [createModalOpen, setCreateModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [selectedServiceOrder, setSelectedServiceOrder] = useState<ServiceOrder | null>(null)

  // Hook para dados reais da API
  const { data: serviceOrders, loading, error, refetch } = useServiceOrders()

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDENTE':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Pendente</Badge>
      case 'EM_ANDAMENTO':
        return <Badge variant="default"><TrendingUp className="w-3 h-3 mr-1" />Em Andamento</Badge>
      case 'CONCLUIDA':
        return <Badge variant="outline" className="text-green-700 border-green-300"><CheckCircle className="w-3 h-3 mr-1" />Concluída</Badge>
      case 'CANCELADA':
        return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Cancelada</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Filtrar ordens baseado no termo de busca
  const filteredOrders = useMemo(() => {
    if (!serviceOrders) return []

    return serviceOrders.filter(order =>
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (order.description && order.description.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  }, [serviceOrders, searchTerm])

  // Handlers para os modais
  const handleCreateServiceOrder = () => {
    setCreateModalOpen(true)
  }

  const handleEditServiceOrder = (serviceOrder: ServiceOrder) => {
    setSelectedServiceOrder(serviceOrder)
    setEditModalOpen(true)
  }

  const handleViewServiceOrder = (serviceOrder: ServiceOrder) => {
    navigate(`/service-orders/${serviceOrder.id}`)
  }

  const handleDeleteServiceOrder = (serviceOrder: ServiceOrder) => {
    setSelectedServiceOrder(serviceOrder)
    setDeleteModalOpen(true)
  }

  const handleModalSuccess = () => {
    refetch()
  }

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Ordens de Serviço</h1>
            <p className="text-gray-600">Gerencie todas as ordens de serviço do sistema</p>
          </div>
          <Button onClick={handleCreateServiceOrder}>
            <Plus className="w-4 h-4 mr-2" />
            Nova Ordem
          </Button>
        </div>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Carregando ordens de serviço...</span>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Ordens de Serviço</h1>
            <p className="text-gray-600">Gerencie todas as ordens de serviço do sistema</p>
          </div>
          <Button onClick={handleCreateServiceOrder}>
            <Plus className="w-4 h-4 mr-2" />
            Nova Ordem
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center h-64">
          <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
          <span className="text-red-600 mb-4">Erro ao carregar ordens: {error}</span>
          <Button onClick={refetch} variant="outline">
            Tentar Novamente
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Ordens de Serviço</h1>
          <p className="text-gray-600">Gerencie todas as ordens de serviço do sistema</p>
        </div>
        <Button onClick={handleCreateServiceOrder}>
          <Plus className="w-4 h-4 mr-2" />
          Nova Ordem
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
          <CardDescription>
            Use os filtros abaixo para encontrar ordens específicas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por número da OS, cliente ou descrição..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filtros Avançados
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Ordens</CardTitle>
          <CardDescription>
            {filteredOrders.length} ordem(ns) encontrada(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Número da OS</TableHead>
                  <TableHead>Cliente</TableHead>
                  <TableHead>Técnico</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Descrição</TableHead>
                  <TableHead>Data</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrders.map((order) => (
                  <TableRow
                    key={order.id}
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleViewServiceOrder(order)}
                  >
                    <TableCell className="font-medium">
                      {order.orderNumber}
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{order.clientName}</p>
                        <p className="text-sm text-gray-500">{order.clientEmail}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      {order.technicianName || (
                        <span className="text-gray-400 italic">Não atribuído</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(order.status)}
                    </TableCell>
                    <TableCell className="max-w-xs">
                      <p className="truncate" title={order.description || ''}>
                        {order.description || 'Sem descrição'}
                      </p>
                    </TableCell>
                    <TableCell>
                      {new Date(order.createdAt).toLocaleDateString('pt-BR')}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleViewServiceOrder(order)
                          }}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleEditServiceOrder(order)
                          }}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteServiceOrder(order)
                          }}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {filteredOrders.length === 0 && serviceOrders && serviceOrders.length > 0 && (
              <div className="text-center py-8 text-gray-500">
                <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Nenhuma ordem encontrada para "{searchTerm}"</p>
              </div>
            )}
            {serviceOrders && serviceOrders.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Nenhuma ordem de serviço cadastrada</p>
                <Button className="mt-4">
                  <Plus className="w-4 h-4 mr-2" />
                  Criar primeira ordem
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Modais */}
      <CreateServiceOrderModal
        open={createModalOpen}
        onOpenChange={setCreateModalOpen}
        onSuccess={handleModalSuccess}
      />

      <EditServiceOrderModal
        serviceOrder={selectedServiceOrder}
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        onSuccess={handleModalSuccess}
      />

      <DeleteServiceOrderModal
        serviceOrder={selectedServiceOrder}
        open={deleteModalOpen}
        onOpenChange={setDeleteModalOpen}
        onSuccess={handleModalSuccess}
      />
    </div>
  )
}
