import { useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Package,
  Hash,
  Tag,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { useEquipment, useEquipmentStats } from '@/hooks'
import { equipmentService } from '@/services'
import { CreateEquipmentModal } from '@/components/modals/CreateEquipmentModal'
import { EditEquipmentModal } from '@/components/modals/EditEquipmentModal'
import { DeleteEquipmentModal } from '@/components/modals/DeleteEquipmentModal'
import type { Equipment } from '@/types'

export default function Equipment() {
  const [searchTerm, setSearchTerm] = useState('')
  const [createModalOpen, setCreateModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [selectedEquipment, setSelectedEquipment] = useState<Equipment | null>(null)

  // Hooks para dados reais da API
  const { data: equipment, loading, error, refetch } = useEquipment()
  const { data: equipmentStats, loading: statsLoading } = useEquipmentStats()

  // Filtrar equipamentos baseado no termo de busca
  const filteredEquipment = useMemo(() => {
    if (!equipment) return []

    return equipment.filter(item =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.model && item.model.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (item.brand && item.brand.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (item.serialNumber && item.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  }, [equipment, searchTerm])

  // Handlers para os modais
  const handleCreateEquipment = () => {
    setCreateModalOpen(true)
  }

  const handleEditEquipment = (equipment: Equipment) => {
    setSelectedEquipment(equipment)
    setEditModalOpen(true)
  }

  const handleDeleteEquipment = (equipment: Equipment) => {
    setSelectedEquipment(equipment)
    setDeleteModalOpen(true)
  }

  const handleModalSuccess = () => {
    refetch()
  }

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Equipamentos</h1>
            <p className="text-gray-600">Gerencie todos os equipamentos do sistema</p>
          </div>
          <Button onClick={handleCreateEquipment}>
            <Plus className="w-4 h-4 mr-2" />
            Novo Equipamento
          </Button>
        </div>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Carregando equipamentos...</span>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Equipamentos</h1>
            <p className="text-gray-600">Gerencie todos os equipamentos do sistema</p>
          </div>
          <Button onClick={handleCreateEquipment}>
            <Plus className="w-4 h-4 mr-2" />
            Novo Equipamento
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center h-64">
          <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
          <span className="text-red-600 mb-4">Erro ao carregar equipamentos: {error}</span>
          <Button onClick={refetch} variant="outline">
            Tentar Novamente
          </Button>
        </div>
      </div>
    )
  }

  const getStatusBadge = (item: any) => {
    const status = equipmentService.getStatus(item)

    switch (status) {
      case 'Ativo':
        return <Badge variant="outline" className="text-green-700 border-green-300">Ativo</Badge>
      case 'Manutenção':
        return <Badge variant="default">Manutenção</Badge>
      case 'Inativo':
        return <Badge variant="secondary">Inativo</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Equipamentos</h1>
          <p className="text-gray-600">Gerencie todos os equipamentos do sistema</p>
        </div>
        <Button onClick={handleCreateEquipment}>
          <Plus className="w-4 h-4 mr-2" />
          Novo Equipamento
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Equipamentos</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <>
                <div className="text-2xl font-bold">{equipmentStats?.total || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Equipamentos cadastrados
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ativos</CardTitle>
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <>
                <div className="text-2xl font-bold">{equipmentStats?.active || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Em funcionamento
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Em Manutenção</CardTitle>
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <>
                <div className="text-2xl font-bold">{equipmentStats?.maintenance || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Sendo reparados
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inativos</CardTitle>
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <>
                <div className="text-2xl font-bold">{equipmentStats?.inactive || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Fora de uso
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
          <CardDescription>
            Use os filtros abaixo para encontrar equipamentos específicos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nome, modelo, marca ou número de série..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filtros Avançados
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Equipment Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Equipamentos</CardTitle>
          <CardDescription>
            {filteredEquipment.length} equipamento(s) encontrado(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Modelo/Marca</TableHead>
                  <TableHead>Número de Série</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Ordens de Serviço</TableHead>
                  <TableHead>Data de Cadastro</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEquipment.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-gray-500">{item.description}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <Tag className="w-3 h-3 mr-1 text-gray-400" />
                          {item.model}
                        </div>
                        <div className="flex items-center text-sm">
                          <Package className="w-3 h-3 mr-1 text-gray-400" />
                          {item.brand}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm font-mono">
                        <Hash className="w-3 h-3 mr-1 text-gray-400" />
                        {item.serialNumber}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(item)}
                    </TableCell>
                    <TableCell>
                      <span className="font-medium">{item.totalServiceOrders}</span>
                    </TableCell>
                    <TableCell>
                      {new Date(item.createdAt).toLocaleDateString('pt-BR')}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditEquipment(item)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteEquipment(item)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Modais */}
      <CreateEquipmentModal
        open={createModalOpen}
        onOpenChange={setCreateModalOpen}
        onSuccess={handleModalSuccess}
      />

      <EditEquipmentModal
        equipment={selectedEquipment}
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        onSuccess={handleModalSuccess}
      />

      <DeleteEquipmentModal
        equipment={selectedEquipment}
        open={deleteModalOpen}
        onOpenChange={setDeleteModalOpen}
        onSuccess={handleModalSuccess}
      />
    </div>
  )
}
