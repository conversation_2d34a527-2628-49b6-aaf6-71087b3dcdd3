import { apiService } from './api';
import { Client, ClientCreateDTO } from '@/types';

export class ClientService {
  private readonly endpoint = '/clients';

  async getAll(): Promise<Client[]> {
    return apiService.get<Client[]>(this.endpoint);
  }

  async getById(id: number): Promise<Client> {
    return apiService.get<Client>(`${this.endpoint}/${id}`);
  }

  async create(data: ClientCreateDTO): Promise<Client> {
    return apiService.post<Client>(this.endpoint, data);
  }

  async update(id: number, data: Partial<ClientCreateDTO>): Promise<Client> {
    return apiService.put<Client>(`${this.endpoint}/${id}`, data);
  }

  async delete(id: number): Promise<void> {
    return apiService.delete<void>(`${this.endpoint}/${id}`);
  }

  // Métodos específicos para estatísticas
  async getStats(): Promise<{
    total: number;
    active: number;
    newThisMonth: number;
  }> {
    const clients = await this.getAll();
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    
    return {
      total: clients.length,
      active: clients.filter(c => (c.totalServiceOrders || 0) > 0).length,
      newThisMonth: clients.filter(c => 
        new Date(c.createdAt) >= startOfMonth
      ).length,
    };
  }

  async search(term: string): Promise<Client[]> {
    const clients = await this.getAll();
    const searchTerm = term.toLowerCase();
    
    return clients.filter(client =>
      client.name.toLowerCase().includes(searchTerm) ||
      (client.email && client.email.toLowerCase().includes(searchTerm)) ||
      (client.phone && client.phone.includes(searchTerm))
    );
  }
}

export const clientService = new ClientService();
