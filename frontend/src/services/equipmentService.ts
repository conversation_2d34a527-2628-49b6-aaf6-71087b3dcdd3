import { apiService } from './api';
import { Equipment, EquipmentCreateDTO } from '@/types';

export class EquipmentService {
  private readonly endpoint = '/equipments';

  async getAll(): Promise<Equipment[]> {
    return apiService.get<Equipment[]>(this.endpoint);
  }

  async getById(id: number): Promise<Equipment> {
    return apiService.get<Equipment>(`${this.endpoint}/${id}`);
  }

  async create(data: EquipmentCreateDTO): Promise<Equipment> {
    return apiService.post<Equipment>(this.endpoint, data);
  }

  async update(id: number, data: Partial<EquipmentCreateDTO>): Promise<Equipment> {
    return apiService.put<Equipment>(`${this.endpoint}/${id}`, data);
  }

  async delete(id: number): Promise<void> {
    return apiService.delete<void>(`${this.endpoint}/${id}`);
  }

  // Métodos específicos para estatísticas
  async getStats(): Promise<{
    total: number;
    active: number;
    maintenance: number;
    inactive: number;
  }> {
    const equipment = await this.getAll();
    
    // Como o backend não tem status, vamos simular baseado no número de ordens
    return {
      total: equipment.length,
      active: equipment.filter(e => (e.totalServiceOrders || 0) > 0).length,
      maintenance: 0, // Simulado - seria baseado em um campo status
      inactive: equipment.filter(e => (e.totalServiceOrders || 0) === 0).length,
    };
  }

  async search(term: string): Promise<Equipment[]> {
    const equipment = await this.getAll();
    const searchTerm = term.toLowerCase();
    
    return equipment.filter(item =>
      item.name.toLowerCase().includes(searchTerm) ||
      (item.model && item.model.toLowerCase().includes(searchTerm)) ||
      (item.brand && item.brand.toLowerCase().includes(searchTerm)) ||
      (item.serialNumber && item.serialNumber.toLowerCase().includes(searchTerm))
    );
  }

  // Método para determinar status baseado no uso (simulado)
  getStatus(equipment: Equipment): 'Ativo' | 'Manutenção' | 'Inativo' {
    const serviceOrders = equipment.totalServiceOrders || 0;
    
    // Lógica simulada para status
    if (serviceOrders > 0) return 'Ativo';
    return 'Inativo';
  }
}

export const equipmentService = new EquipmentService();
