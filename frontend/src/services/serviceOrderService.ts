import { apiService } from './api';
import { ServiceOrder, ServiceOrderCreateDTO, ServiceOrderStatusUpdateDTO } from '@/types';

export class ServiceOrderService {
  private readonly endpoint = '/service-orders';

  async getAll(): Promise<ServiceOrder[]> {
    return apiService.get<ServiceOrder[]>(this.endpoint);
  }

  async getById(id: number): Promise<ServiceOrder> {
    return apiService.get<ServiceOrder>(`${this.endpoint}/${id}`);
  }

  async create(data: ServiceOrderCreateDTO): Promise<ServiceOrder> {
    return apiService.post<ServiceOrder>(this.endpoint, data);
  }

  async update(id: number, data: Partial<ServiceOrderCreateDTO>): Promise<ServiceOrder> {
    return apiService.put<ServiceOrder>(`${this.endpoint}/${id}`, data);
  }

  async updateStatus(id: number, data: ServiceOrderStatusUpdateDTO): Promise<ServiceOrder> {
    return apiService.put<ServiceOrder>(`${this.endpoint}/${id}/status`, data);
  }

  async delete(id: number): Promise<void> {
    return apiService.delete<void>(`${this.endpoint}/${id}`);
  }

  // Métodos específicos para estatísticas
  async getStats(): Promise<{
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
    cancelled: number;
  }> {
    const orders = await this.getAll();
    
    return {
      total: orders.length,
      pending: orders.filter(o => o.status === 'PENDENTE').length,
      inProgress: orders.filter(o => o.status === 'EM_ANDAMENTO').length,
      completed: orders.filter(o => o.status === 'CONCLUIDA').length,
      cancelled: orders.filter(o => o.status === 'CANCELADA').length,
    };
  }

  async getRecent(limit: number = 5): Promise<ServiceOrder[]> {
    const orders = await this.getAll();
    return orders
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit);
  }
}

export const serviceOrderService = new ServiceOrderService();
