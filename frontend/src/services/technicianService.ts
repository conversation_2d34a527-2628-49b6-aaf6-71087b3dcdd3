import { apiService } from './api';
import { Technician, TechnicianCreateDTO } from '@/types';

export class TechnicianService {
  private readonly endpoint = '/technicians';

  async getAll(): Promise<Technician[]> {
    return apiService.get<Technician[]>(this.endpoint);
  }

  async getById(id: number): Promise<Technician> {
    return apiService.get<Technician>(`${this.endpoint}/${id}`);
  }

  async create(data: TechnicianCreateDTO): Promise<Technician> {
    return apiService.post<Technician>(this.endpoint, data);
  }

  async update(id: number, data: Partial<TechnicianCreateDTO>): Promise<Technician> {
    return apiService.put<Technician>(`${this.endpoint}/${id}`, data);
  }

  async delete(id: number): Promise<void> {
    return apiService.delete<void>(`${this.endpoint}/${id}`);
  }

  // Métodos específicos para estatísticas
  async getStats(): Promise<{
    total: number;
    available: number;
    active: number;
    totalActiveOrders: number;
  }> {
    const technicians = await this.getAll();
    
    return {
      total: technicians.length,
      available: technicians.filter(t => (t.totalServiceOrders || 0) === 0).length,
      active: technicians.filter(t => (t.totalServiceOrders || 0) > 0).length,
      totalActiveOrders: technicians.reduce((sum, t) => sum + (t.totalServiceOrders || 0), 0),
    };
  }

  async search(term: string): Promise<Technician[]> {
    const technicians = await this.getAll();
    const searchTerm = term.toLowerCase();
    
    return technicians.filter(technician =>
      technician.name.toLowerCase().includes(searchTerm) ||
      (technician.email && technician.email.toLowerCase().includes(searchTerm)) ||
      (technician.specialization && technician.specialization.toLowerCase().includes(searchTerm))
    );
  }

  // Método para calcular carga de trabalho (simulado baseado no total de ordens)
  getWorkload(technician: Technician): 'available' | 'low' | 'medium' | 'high' {
    const activeOrders = technician.totalServiceOrders || 0;
    
    if (activeOrders === 0) return 'available';
    if (activeOrders <= 2) return 'low';
    if (activeOrders <= 4) return 'medium';
    return 'high';
  }
}

export const technicianService = new TechnicianService();
