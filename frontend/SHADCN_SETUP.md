# shadcn/ui Setup Guide

Este documento descreve a configuração do shadcn/ui no projeto Service Order Manager.

## ✅ Configuração Completa

### 📦 Dependências Instaladas

```json
{
  "dependencies": {
    "@radix-ui/react-slot": "^1.1.1",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "tailwind-merge": "^2.6.0"
  },
  "devDependencies": {
    "@types/node": "^22.10.7",
    "tailwindcss-animate": "^1.0.7"
  }
}
```

### 🔧 Arquivos de Configuração

#### `components.json`
```json
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "default",
  "rsc": false,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.ts",
    "css": "src/index.css",
    "baseColor": "slate",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils",
    "ui": "@/components/ui"
  }
}
```

### 🎨 Componentes Disponíveis

#### Instalados:
- ✅ **Button** - `@/components/ui/button`
- ✅ **Card** - `@/components/ui/card`
- ✅ **Input** - `@/components/ui/input`
- ✅ **Label** - `@/components/ui/label`
- ✅ **Badge** - `@/components/ui/badge`
- ✅ **Table** - `@/components/ui/table`
- ✅ **Sheet** - `@/components/ui/sheet`
- ✅ **Sidebar** - `@/components/ui/sidebar`

### 📝 Comandos Úteis

#### Adicionar novos componentes:
```bash
npx shadcn@latest add [component-name]
```

### 🔍 Utilitários

#### `src/lib/utils.ts`
Função `cn()` para combinar classes CSS:
```tsx
import { cn } from '@/lib/utils'

<div className={cn("base-classes", condition && "conditional-classes")} />
```

### 📚 Documentação

- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [Radix UI Documentation](https://www.radix-ui.com/)
