package br.com.pedroabib.osmanager.os_manager.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.pedroabib.osmanager.os_manager.entities.Equipment;

@Repository
public interface EquipmentRepository extends JpaRepository<Equipment, Long> {

    Optional<Equipment> findBySerialNumber(String serialNumber);

    List<Equipment> findByNameContainingIgnoreCase(String name);

    List<Equipment> findByBrandContainingIgnoreCase(String brand);

    List<Equipment> findByModelContainingIgnoreCase(String model);

    @Query("SELECT e FROM Equipment e WHERE e.brand = :brand AND e.model = :model")
    List<Equipment> findByBrandAndModel(@Param("brand") String brand, @Param("model") String model);

    boolean existsBySerialNumber(String serialNumber);
}
