package br.com.pedroabib.osmanager.os_manager.repositories;

import br.com.pedroabib.osmanager.os_manager.entities.ServiceOrder;
import br.com.pedroabib.osmanager.os_manager.enums.ServiceOrderStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ServiceOrderRepository extends JpaRepository<ServiceOrder, Long> {

    Optional<ServiceOrder> findByOrderNumber(String orderNumber);

    List<ServiceOrder> findByStatus(ServiceOrderStatus status);

    @Query("SELECT so FROM ServiceOrder so LEFT JOIN FETCH so.client LEFT JOIN FETCH so.technician LEFT JOIN FETCH so.equipments")
    List<ServiceOrder> findAllWithDetails();

    @Query("SELECT so FROM ServiceOrder so LEFT JOIN FETCH so.client LEFT JOIN FETCH so.technician LEFT JOIN FETCH so.equipments WHERE so.id = :id")
    Optional<ServiceOrder> findByIdWithDetails(@Param("id") Long id);

    List<ServiceOrder> findByClientId(Long clientId);

    List<ServiceOrder> findByTechnicianId(Long technicianId);
}
