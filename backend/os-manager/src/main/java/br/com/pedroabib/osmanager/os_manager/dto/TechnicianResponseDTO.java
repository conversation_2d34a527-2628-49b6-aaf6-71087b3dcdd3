package br.com.pedroabib.osmanager.os_manager.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TechnicianResponseDTO {

    private Long id;

    private String name;

    private String email;

    private String phone;

    private String specialization;

    private LocalDateTime createdAt;

    private Integer totalServiceOrders;
}
