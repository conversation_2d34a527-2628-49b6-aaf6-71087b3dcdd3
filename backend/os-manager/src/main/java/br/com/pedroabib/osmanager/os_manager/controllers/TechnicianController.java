package br.com.pedroabib.osmanager.os_manager.controllers;

import br.com.pedroabib.osmanager.os_manager.dto.TechnicianCreateDTO;
import br.com.pedroabib.osmanager.os_manager.dto.TechnicianResponseDTO;
import br.com.pedroabib.osmanager.os_manager.dto.TechnicianUpdateDTO;
import br.com.pedroabib.osmanager.os_manager.services.TechnicianService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/technicians")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class TechnicianController {

    private final TechnicianService technicianService;

    /**
     * Criar um novo Técnico
     */
    @PostMapping
    public ResponseEntity<TechnicianResponseDTO> createTechnician(@RequestBody TechnicianCreateDTO createDTO) {
        try {
            TechnicianResponseDTO createdTechnician = technicianService.createTechnician(createDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdTechnician);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Listar todos os Técnicos
     */
    @GetMapping
    public ResponseEntity<List<TechnicianResponseDTO>> getAllTechnicians() {
        List<TechnicianResponseDTO> technicians = technicianService.getAllTechnicians();
        return ResponseEntity.ok(technicians);
    }

    /**
     * Buscar Técnico por ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<TechnicianResponseDTO> getTechnicianById(@PathVariable Long id) {
        try {
            TechnicianResponseDTO technician = technicianService.getTechnicianById(id);
            return ResponseEntity.ok(technician);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Atualizar Técnico
     */
    @PutMapping("/{id}")
    public ResponseEntity<TechnicianResponseDTO> updateTechnician(
            @PathVariable Long id,
            @RequestBody TechnicianUpdateDTO updateDTO) {
        try {
            TechnicianResponseDTO updatedTechnician = technicianService.updateTechnician(id, updateDTO);
            return ResponseEntity.ok(updatedTechnician);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Deletar Técnico
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTechnician(@PathVariable Long id) {
        try {
            technicianService.deleteTechnician(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Buscar Técnicos por nome
     */
    @GetMapping("/search")
    public ResponseEntity<List<TechnicianResponseDTO>> searchTechniciansByName(@RequestParam String name) {
        List<TechnicianResponseDTO> technicians = technicianService.searchTechniciansByName(name);
        return ResponseEntity.ok(technicians);
    }

    /**
     * Buscar Técnicos por especialização
     */
    @GetMapping("/search/specialization")
    public ResponseEntity<List<TechnicianResponseDTO>> searchTechniciansBySpecialization(@RequestParam String specialization) {
        List<TechnicianResponseDTO> technicians = technicianService.searchTechniciansBySpecialization(specialization);
        return ResponseEntity.ok(technicians);
    }
}
