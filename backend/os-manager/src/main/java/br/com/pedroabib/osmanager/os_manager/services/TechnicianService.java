package br.com.pedroabib.osmanager.os_manager.services;

import br.com.pedroabib.osmanager.os_manager.dto.TechnicianCreateDTO;
import br.com.pedroabib.osmanager.os_manager.dto.TechnicianResponseDTO;
import br.com.pedroabib.osmanager.os_manager.dto.TechnicianUpdateDTO;
import br.com.pedroabib.osmanager.os_manager.entities.Technician;
import br.com.pedroabib.osmanager.os_manager.repositories.TechnicianRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class TechnicianService {

    private final TechnicianRepository technicianRepository;

    public TechnicianResponseDTO createTechnician(TechnicianCreateDTO createDTO) {
        // Validar se email já existe
        if (createDTO.getEmail() != null && technicianRepository.existsByEmail(createDTO.getEmail())) {
            throw new RuntimeException("Email já está em uso: " + createDTO.getEmail());
        }

        // Validar campos obrigatórios
        if (createDTO.getName() == null || createDTO.getName().trim().isEmpty()) {
            throw new RuntimeException("Nome é obrigatório");
        }

        Technician technician = new Technician();
        technician.setName(createDTO.getName().trim());
        technician.setEmail(createDTO.getEmail());
        technician.setPhone(createDTO.getPhone());
        technician.setSpecialization(createDTO.getSpecialization());

        Technician savedTechnician = technicianRepository.save(technician);
        return convertToResponseDTO(savedTechnician);
    }

    @Transactional(readOnly = true)
    public List<TechnicianResponseDTO> getAllTechnicians() {
        List<Technician> technicians = technicianRepository.findAll();
        return technicians.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public TechnicianResponseDTO getTechnicianById(Long id) {
        Technician technician = technicianRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Técnico não encontrado com ID: " + id));
        return convertToResponseDTO(technician);
    }

    public TechnicianResponseDTO updateTechnician(Long id, TechnicianUpdateDTO updateDTO) {
        Technician technician = technicianRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Técnico não encontrado com ID: " + id));

        // Validar email único se estiver sendo alterado
        if (updateDTO.getEmail() != null && !updateDTO.getEmail().equals(technician.getEmail())) {
            if (technicianRepository.existsByEmail(updateDTO.getEmail())) {
                throw new RuntimeException("Email já está em uso: " + updateDTO.getEmail());
            }
        }

        // Atualizar campos se fornecidos
        if (updateDTO.getName() != null && !updateDTO.getName().trim().isEmpty()) {
            technician.setName(updateDTO.getName().trim());
        }
        if (updateDTO.getEmail() != null) {
            technician.setEmail(updateDTO.getEmail());
        }
        if (updateDTO.getPhone() != null) {
            technician.setPhone(updateDTO.getPhone());
        }
        if (updateDTO.getSpecialization() != null) {
            technician.setSpecialization(updateDTO.getSpecialization());
        }

        Technician updatedTechnician = technicianRepository.save(technician);
        return convertToResponseDTO(updatedTechnician);
    }

    public void deleteTechnician(Long id) {
        Technician technician = technicianRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Técnico não encontrado com ID: " + id));

        // Verificar se tem ordens de serviço associadas
        if (technician.getServiceOrders() != null && !technician.getServiceOrders().isEmpty()) {
            throw new RuntimeException("Não é possível excluir técnico com ordens de serviço associadas");
        }

        technicianRepository.delete(technician);
    }

    @Transactional(readOnly = true)
    public List<TechnicianResponseDTO> searchTechniciansByName(String name) {
        List<Technician> technicians = technicianRepository.findByNameContainingIgnoreCase(name);
        return technicians.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<TechnicianResponseDTO> searchTechniciansBySpecialization(String specialization) {
        List<Technician> technicians = technicianRepository.findBySpecializationContainingIgnoreCase(specialization);
        return technicians.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }

    private TechnicianResponseDTO convertToResponseDTO(Technician technician) {
        TechnicianResponseDTO dto = new TechnicianResponseDTO();
        dto.setId(technician.getId());
        dto.setName(technician.getName());
        dto.setEmail(technician.getEmail());
        dto.setPhone(technician.getPhone());
        dto.setSpecialization(technician.getSpecialization());
        dto.setCreatedAt(technician.getCreatedAt());
        
        // Contar ordens de serviço
        if (technician.getServiceOrders() != null) {
            dto.setTotalServiceOrders(technician.getServiceOrders().size());
        } else {
            dto.setTotalServiceOrders(0);
        }

        return dto;
    }
}
