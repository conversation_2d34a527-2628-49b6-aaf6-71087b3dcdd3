package br.com.pedroabib.osmanager.os_manager.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.pedroabib.osmanager.os_manager.entities.Technician;

@Repository
public interface TechnicianRepository extends JpaRepository<Technician, Long> {

    Optional<Technician> findByEmail(String email);

    List<Technician> findByNameContainingIgnoreCase(String name);

    List<Technician> findBySpecializationContainingIgnoreCase(String specialization);

    @Query("SELECT t FROM Technician t WHERE t.phone = :phone")
    Optional<Technician> findByPhone(@Param("phone") String phone);

    boolean existsByEmail(String email);
}
