package br.com.pedroabib.osmanager.os_manager.controllers;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import br.com.pedroabib.osmanager.os_manager.dto.EquipmentCreateDTO;
import br.com.pedroabib.osmanager.os_manager.dto.EquipmentResponseDTO;
import br.com.pedroabib.osmanager.os_manager.dto.EquipmentUpdateDTO;
import br.com.pedroabib.osmanager.os_manager.services.EquipmentService;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/equipments")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class EquipmentController {

    private final EquipmentService equipmentService;

    /**
     * Criar um novo Equipamento
     */
    @PostMapping
    public ResponseEntity<EquipmentResponseDTO> createEquipment(@RequestBody EquipmentCreateDTO createDTO) {
        try {
            EquipmentResponseDTO createdEquipment = equipmentService.createEquipment(createDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdEquipment);
        } catch (RuntimeException e) {
            System.err.println("Erro ao criar equipamento: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Listar todos os Equipamentos
     */
    @GetMapping
    public ResponseEntity<List<EquipmentResponseDTO>> getAllEquipments() {
        List<EquipmentResponseDTO> equipments = equipmentService.getAllEquipments();
        return ResponseEntity.ok(equipments);
    }

    /**
     * Buscar Equipamento por ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<EquipmentResponseDTO> getEquipmentById(@PathVariable Long id) {
        try {
            EquipmentResponseDTO equipment = equipmentService.getEquipmentById(id);
            return ResponseEntity.ok(equipment);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Atualizar Equipamento
     */
    @PutMapping("/{id}")
    public ResponseEntity<EquipmentResponseDTO> updateEquipment(
            @PathVariable Long id,
            @RequestBody EquipmentUpdateDTO updateDTO) {
        try {
            EquipmentResponseDTO updatedEquipment = equipmentService.updateEquipment(id, updateDTO);
            return ResponseEntity.ok(updatedEquipment);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Deletar Equipamento
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEquipment(@PathVariable Long id) {
        try {
            equipmentService.deleteEquipment(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Buscar Equipamentos por nome
     */
    @GetMapping("/search")
    public ResponseEntity<List<EquipmentResponseDTO>> searchEquipmentsByName(@RequestParam String name) {
        List<EquipmentResponseDTO> equipments = equipmentService.searchEquipmentsByName(name);
        return ResponseEntity.ok(equipments);
    }

    /**
     * Buscar Equipamentos por marca
     */
    @GetMapping("/search/brand")
    public ResponseEntity<List<EquipmentResponseDTO>> searchEquipmentsByBrand(@RequestParam String brand) {
        List<EquipmentResponseDTO> equipments = equipmentService.searchEquipmentsByBrand(brand);
        return ResponseEntity.ok(equipments);
    }

    /**
     * Buscar Equipamentos por modelo
     */
    @GetMapping("/search/model")
    public ResponseEntity<List<EquipmentResponseDTO>> searchEquipmentsByModel(@RequestParam String model) {
        List<EquipmentResponseDTO> equipments = equipmentService.searchEquipmentsByModel(model);
        return ResponseEntity.ok(equipments);
    }
}
