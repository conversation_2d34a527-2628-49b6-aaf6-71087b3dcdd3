package br.com.pedroabib.osmanager.os_manager.dto;

import br.com.pedroabib.osmanager.os_manager.enums.ServiceOrderStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceOrderResponseDTO {

    private Long id;
    private String orderNumber;
    private LocalDateTime createdAt;
    private ServiceOrderStatus status;
    private String description;
    private String observations;

    // Client info
    private Long clientId;
    private String clientName;
    private String clientEmail;

    // Technician info
    private Long technicianId;
    private String technicianName;
    private String technicianEmail;

    // Equipment info
    private List<EquipmentSummaryDTO> equipments;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EquipmentSummaryDTO {
        private Long id;
        private String name;
        private String model;
        private String brand;
        private String serialNumber;
    }
}
