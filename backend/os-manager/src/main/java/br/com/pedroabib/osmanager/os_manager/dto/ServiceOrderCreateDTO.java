package br.com.pedroabib.osmanager.os_manager.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceOrderCreateDTO {

    private Long clientId; // Obrigatório

    private Long technicianId;

    private List<Long> equipmentIds;

    private String description;

    private String observations;
}
