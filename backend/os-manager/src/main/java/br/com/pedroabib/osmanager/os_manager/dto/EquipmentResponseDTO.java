package br.com.pedroabib.osmanager.os_manager.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EquipmentResponseDTO {

    private Long id;

    private String name;

    private String model;

    private String brand;

    private String serialNumber;

    private String description;

    private LocalDateTime createdAt;

    private Integer totalServiceOrders;
}
