package br.com.pedroabib.osmanager.os_manager.controllers;

import br.com.pedroabib.osmanager.os_manager.dto.ClientCreateDTO;
import br.com.pedroabib.osmanager.os_manager.dto.ClientResponseDTO;
import br.com.pedroabib.osmanager.os_manager.dto.ClientUpdateDTO;
import br.com.pedroabib.osmanager.os_manager.services.ClientService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/clients")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class ClientController {

    private final ClientService clientService;

    /**
     * Criar um novo Cliente
     */
    @PostMapping
    public ResponseEntity<ClientResponseDTO> createClient(@RequestBody ClientCreateDTO createDTO) {
        try {
            ClientResponseDTO createdClient = clientService.createClient(createDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdClient);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Listar todos os Clientes
     */
    @GetMapping
    public ResponseEntity<List<ClientResponseDTO>> getAllClients() {
        List<ClientResponseDTO> clients = clientService.getAllClients();
        return ResponseEntity.ok(clients);
    }

    /**
     * Buscar Cliente por ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<ClientResponseDTO> getClientById(@PathVariable Long id) {
        try {
            ClientResponseDTO client = clientService.getClientById(id);
            return ResponseEntity.ok(client);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Atualizar Cliente
     */
    @PutMapping("/{id}")
    public ResponseEntity<ClientResponseDTO> updateClient(
            @PathVariable Long id,
            @RequestBody ClientUpdateDTO updateDTO) {
        try {
            ClientResponseDTO updatedClient = clientService.updateClient(id, updateDTO);
            return ResponseEntity.ok(updatedClient);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Deletar Cliente
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteClient(@PathVariable Long id) {
        try {
            clientService.deleteClient(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Buscar Clientes por nome
     */
    @GetMapping("/search")
    public ResponseEntity<List<ClientResponseDTO>> searchClientsByName(@RequestParam String name) {
        List<ClientResponseDTO> clients = clientService.searchClientsByName(name);
        return ResponseEntity.ok(clients);
    }
}
