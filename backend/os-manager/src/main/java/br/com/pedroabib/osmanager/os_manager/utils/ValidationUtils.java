package br.com.pedroabib.osmanager.os_manager.utils;

import br.com.pedroabib.osmanager.os_manager.exceptions.ValidationException;

import java.util.regex.Pattern;

public class ValidationUtils {

    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    );
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^\\(?\\d{2}\\)?\\s?\\d{4,5}-?\\d{4}$"
    );
    
    private static final Pattern SERIAL_NUMBER_PATTERN = Pattern.compile(
        "^[A-Za-z0-9]{3,20}$"
    );

    public static void validateRequired(String value, String fieldName) {
        if (value == null || value.trim().isEmpty()) {
            throw new ValidationException(fieldName, "é obrigatório");
        }
    }

    public static void validateEmail(String email) {
        if (email != null && !email.trim().isEmpty()) {
            if (!EMAIL_PATTERN.matcher(email).matches()) {
                throw new ValidationException("email", "formato inválido");
            }
        }
    }

    public static void validatePhone(String phone) {
        if (phone != null && !phone.trim().isEmpty()) {
            if (!PHONE_PATTERN.matcher(phone).matches()) {
                throw new ValidationException("telefone", "formato inválido. Use: (11) 99999-9999 ou 11999999999");
            }
        }
    }

    public static void validateSerialNumber(String serialNumber) {
        if (serialNumber != null && !serialNumber.trim().isEmpty()) {
            if (!SERIAL_NUMBER_PATTERN.matcher(serialNumber).matches()) {
                throw new ValidationException("serialNumber", "deve conter apenas letras e números (3-20 caracteres)");
            }
        }
    }

    public static void validateStringLength(String value, String fieldName, int minLength, int maxLength) {
        if (value != null) {
            if (value.length() < minLength) {
                throw new ValidationException(fieldName, String.format("deve ter pelo menos %d caracteres", minLength));
            }
            if (value.length() > maxLength) {
                throw new ValidationException(fieldName, String.format("deve ter no máximo %d caracteres", maxLength));
            }
        }
    }

    public static String sanitizeString(String value) {
        if (value == null) return null;
        return value.trim().replaceAll("\\s+", " ");
    }
}
