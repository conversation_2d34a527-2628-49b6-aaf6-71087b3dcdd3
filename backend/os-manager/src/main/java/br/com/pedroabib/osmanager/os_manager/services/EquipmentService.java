package br.com.pedroabib.osmanager.os_manager.services;

import br.com.pedroabib.osmanager.os_manager.dto.EquipmentCreateDTO;
import br.com.pedroabib.osmanager.os_manager.dto.EquipmentResponseDTO;
import br.com.pedroabib.osmanager.os_manager.dto.EquipmentUpdateDTO;
import br.com.pedroabib.osmanager.os_manager.entities.Equipment;
import br.com.pedroabib.osmanager.os_manager.repositories.EquipmentRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class EquipmentService {

    private final EquipmentRepository equipmentRepository;

    public EquipmentResponseDTO createEquipment(EquipmentCreateDTO createDTO) {
        // Validar se serial number já existe
        if (createDTO.getSerialNumber() != null && equipmentRepository.existsBySerialNumber(createDTO.getSerialNumber())) {
            throw new RuntimeException("Número de série já está em uso: " + createDTO.getSerialNumber());
        }

        // Validar campos obrigatórios
        if (createDTO.getName() == null || createDTO.getName().trim().isEmpty()) {
            throw new RuntimeException("Nome é obrigatório");
        }

        Equipment equipment = new Equipment();
        equipment.setName(createDTO.getName().trim());
        equipment.setModel(createDTO.getModel());
        equipment.setBrand(createDTO.getBrand());
        equipment.setSerialNumber(createDTO.getSerialNumber());
        equipment.setDescription(createDTO.getDescription());

        Equipment savedEquipment = equipmentRepository.save(equipment);
        return convertToResponseDTO(savedEquipment);
    }

    @Transactional(readOnly = true)
    public List<EquipmentResponseDTO> getAllEquipments() {
        List<Equipment> equipments = equipmentRepository.findAll();
        return equipments.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public EquipmentResponseDTO getEquipmentById(Long id) {
        Equipment equipment = equipmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Equipamento não encontrado com ID: " + id));
        return convertToResponseDTO(equipment);
    }

    public EquipmentResponseDTO updateEquipment(Long id, EquipmentUpdateDTO updateDTO) {
        Equipment equipment = equipmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Equipamento não encontrado com ID: " + id));

        // Validar serial number único se estiver sendo alterado
        if (updateDTO.getSerialNumber() != null && !updateDTO.getSerialNumber().equals(equipment.getSerialNumber())) {
            if (equipmentRepository.existsBySerialNumber(updateDTO.getSerialNumber())) {
                throw new RuntimeException("Número de série já está em uso: " + updateDTO.getSerialNumber());
            }
        }

        // Atualizar campos se fornecidos
        if (updateDTO.getName() != null && !updateDTO.getName().trim().isEmpty()) {
            equipment.setName(updateDTO.getName().trim());
        }
        if (updateDTO.getModel() != null) {
            equipment.setModel(updateDTO.getModel());
        }
        if (updateDTO.getBrand() != null) {
            equipment.setBrand(updateDTO.getBrand());
        }
        if (updateDTO.getSerialNumber() != null) {
            equipment.setSerialNumber(updateDTO.getSerialNumber());
        }
        if (updateDTO.getDescription() != null) {
            equipment.setDescription(updateDTO.getDescription());
        }

        Equipment updatedEquipment = equipmentRepository.save(equipment);
        return convertToResponseDTO(updatedEquipment);
    }

    public void deleteEquipment(Long id) {
        Equipment equipment = equipmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Equipamento não encontrado com ID: " + id));

        // Verificar se tem ordens de serviço associadas
        if (equipment.getServiceOrders() != null && !equipment.getServiceOrders().isEmpty()) {
            throw new RuntimeException("Não é possível excluir equipamento com ordens de serviço associadas");
        }

        equipmentRepository.delete(equipment);
    }

    @Transactional(readOnly = true)
    public List<EquipmentResponseDTO> searchEquipmentsByName(String name) {
        List<Equipment> equipments = equipmentRepository.findByNameContainingIgnoreCase(name);
        return equipments.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<EquipmentResponseDTO> searchEquipmentsByBrand(String brand) {
        List<Equipment> equipments = equipmentRepository.findByBrandContainingIgnoreCase(brand);
        return equipments.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<EquipmentResponseDTO> searchEquipmentsByModel(String model) {
        List<Equipment> equipments = equipmentRepository.findByModelContainingIgnoreCase(model);
        return equipments.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }

    private EquipmentResponseDTO convertToResponseDTO(Equipment equipment) {
        EquipmentResponseDTO dto = new EquipmentResponseDTO();
        dto.setId(equipment.getId());
        dto.setName(equipment.getName());
        dto.setModel(equipment.getModel());
        dto.setBrand(equipment.getBrand());
        dto.setSerialNumber(equipment.getSerialNumber());
        dto.setDescription(equipment.getDescription());
        dto.setCreatedAt(equipment.getCreatedAt());
        
        // Contar ordens de serviço
        if (equipment.getServiceOrders() != null) {
            dto.setTotalServiceOrders(equipment.getServiceOrders().size());
        } else {
            dto.setTotalServiceOrders(0);
        }

        return dto;
    }
}
